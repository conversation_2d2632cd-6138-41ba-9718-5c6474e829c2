/**
 * Dashboard Route (Protected)
 */

import React from 'react';
import { Layout, Menu, Button, Space, Typography, Card, Row, Col, Statistic } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  TeamOutlined,
  Bar<PERSON><PERSON>Outlined,
  LogoutOutlined,
} from '@ant-design/icons';
import { Link, Outlet, useLocation } from 'react-router';
import type { Route } from './+types/dashboard';
import { requireAuth } from '../lib/rbac/guards/route-guard';
import { authLoader } from '../lib/rbac/loaders/auth.loaders';
import {
  AuthProvider,
  UserProfileCard,
  PermissionGuard,
  RoleGuard,
  Can,
  useAuth,
} from '../lib/rbac/components';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Dashboard - Admin Panel' },
    { name: 'description', content: 'Admin dashboard with role-based access control' },
  ];
}

export async function loader({ request }: Route.LoaderArgs) {
  // Require authentication for dashboard
  const userContext = await requireAuth({ request } as any);
  
  // Also load auth data for the provider
  const authData = await authLoader({ request } as any);
  
  return {
    userContext,
    ...authData,
  };
}

function DashboardContent() {
  const location = useLocation();
  const { user, logout } = useAuth();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">Overview</Link>,
    },
    {
      key: '/dashboard/users',
      icon: <TeamOutlined />,
      label: <Link to="/dashboard/users">Users</Link>,
      // Only show for admin users
      style: { display: user?.effectiveRole?.name === 'admin' ? 'block' : 'none' },
    },
    {
      key: '/dashboard/content',
      icon: <FileTextOutlined />,
      label: <Link to="/dashboard/content">Content</Link>,
    },
    {
      key: '/dashboard/reports',
      icon: <BarChartOutlined />,
      label: <Link to="/dashboard/reports">Reports</Link>,
    },
    {
      key: '/dashboard/settings',
      icon: <SettingOutlined />,
      label: <Link to="/dashboard/settings">Settings</Link>,
    },
  ];

  const handleLogout = async () => {
    await logout();
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={250} theme="light">
        <div className="p-4">
          <Title level={4} className="text-center mb-4">
            Admin Panel
          </Title>
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
        />
        
        <div className="absolute bottom-4 left-4 right-4">
          <UserProfileCard />
        </div>
      </Sider>
      
      <Layout>
        <Header className="bg-white px-6 flex justify-between items-center border-b">
          <Title level={3} className="mb-0">
            Dashboard
          </Title>
          
          <Space>
            <Can resource="settings" action="read">
              <Link to="/dashboard/settings">
                <Button icon={<SettingOutlined />}>Settings</Button>
              </Link>
            </Can>
            
            <Button 
              icon={<LogoutOutlined />} 
              onClick={handleLogout}
              type="text"
            >
              Logout
            </Button>
          </Space>
        </Header>
        
        <Content className="p-6">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}

export default function Dashboard() {
  return (
    <AuthProvider>
      <DashboardContent />
    </AuthProvider>
  );
}

// Default dashboard overview component
export function DashboardOverview() {
  const { user } = useAuth();

  return (
    <div>
      <Title level={2}>Welcome back, {user?.user.firstName}!</Title>
      
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Your Role"
              value={user?.effectiveRole?.displayName || 'No Role'}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Organization"
              value={user?.currentTenant?.name || 'No Tenant'}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Permissions"
              value={user?.resolvedPermissions.length || 0}
              prefix={<SettingOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Resources"
              value={new Set(user?.resolvedPermissions.map(p => p.resource)).size || 0}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Quick Actions" className="h-full">
            <Space direction="vertical" size="middle" className="w-full">
              <Can resource="content" action="create">
                <Button type="primary" icon={<FileTextOutlined />} size="large" block>
                  Create New Content
                </Button>
              </Can>
              
              <Can resource="reports" action="read">
                <Link to="/dashboard/reports">
                  <Button icon={<BarChartOutlined />} size="large" block>
                    View Reports
                  </Button>
                </Link>
              </Can>
              
              <RoleGuard roles={['admin']}>
                <Link to="/dashboard/users">
                  <Button icon={<TeamOutlined />} size="large" block>
                    Manage Users
                  </Button>
                </Link>
              </RoleGuard>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="System Status" className="h-full">
            <Space direction="vertical" size="middle" className="w-full">
              <div className="flex justify-between">
                <span>Authentication:</span>
                <span className="text-green-600">✓ Active</span>
              </div>
              <div className="flex justify-between">
                <span>Authorization:</span>
                <span className="text-green-600">✓ Active</span>
              </div>
              <div className="flex justify-between">
                <span>RBAC System:</span>
                <span className="text-green-600">✓ Operational</span>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
