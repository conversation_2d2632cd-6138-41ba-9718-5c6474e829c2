/**
 * Unauthorized Access Page
 */

import React from 'react';
import type { Route } from './+types/auth.unauthorized';
import { UnauthorizedError } from '../lib/rbac/components/ErrorBoundary';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Unauthorized - Admin Panel' },
    { name: 'description', content: 'Authentication required' },
  ];
}

export default function UnauthorizedPage() {
  return <UnauthorizedError />;
}
