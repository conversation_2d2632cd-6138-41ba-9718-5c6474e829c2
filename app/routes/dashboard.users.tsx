/**
 * Users Management Route (Admin Only)
 */

import React, { useState } from 'react';
import { Table, Button, Space, Tag, Modal, Form, Input, Switch, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons';
import type { Route } from './+types/dashboard.users';
import { requireAdmin } from '../lib/rbac/guards/route-guard';
import { AdminGuard } from '../lib/rbac/components';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'User Management - Admin Panel' },
    { name: 'description', content: 'Manage users and their roles' },
  ];
}

export async function loader({ request }: Route.LoaderArgs) {
  // Require admin role
  const userContext = await requireAdmin({ request } as any);
  
  // Mock user data
  const users = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Admin',
      isActive: true,
      emailVerified: true,
      role: 'Admin',
      tenant: 'Acme Corporation',
      createdAt: '2024-01-15',
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Editor',
      isActive: true,
      emailVerified: true,
      role: 'Editor',
      tenant: 'Acme Corporation',
      createdAt: '2024-01-20',
    },
    {
      id: 'user-3',
      email: '<EMAIL>',
      firstName: 'Bob',
      lastName: 'Viewer',
      isActive: true,
      emailVerified: false,
      role: 'Viewer',
      tenant: 'Beta Industries',
      createdAt: '2024-02-01',
    },
  ];
  
  return { users, userContext };
}

export default function UsersPage({ loaderData }: Route.ComponentProps) {
  const { users } = loaderData;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [form] = Form.useForm();

  const columns = [
    {
      title: 'User',
      key: 'user',
      render: (record: any) => (
        <div className="flex items-center space-x-3">
          <UserOutlined className="text-gray-400" />
          <div>
            <div className="font-medium">{record.firstName} {record.lastName}</div>
            <div className="text-sm text-gray-500">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role === 'Admin' ? 'red' : role === 'Editor' ? 'blue' : 'default'}>
          {role}
        </Tag>
      ),
    },
    {
      title: 'Organization',
      dataIndex: 'tenant',
      key: 'tenant',
    },
    {
      title: 'Status',
      key: 'status',
      render: (record: any) => (
        <Space>
          <Tag color={record.isActive ? 'green' : 'red'}>
            {record.isActive ? 'Active' : 'Inactive'}
          </Tag>
          {record.emailVerified && <Tag color="blue">Verified</Tag>}
        </Space>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  const handleCreate = () => {
    setEditingUser(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (user: any) => {
    setEditingUser(user);
    form.setFieldsValue(user);
    setIsModalVisible(true);
  };

  const handleDelete = (user: any) => {
    Modal.confirm({
      title: 'Delete User',
      content: `Are you sure you want to delete ${user.firstName} ${user.lastName}?`,
      okText: 'Delete',
      okType: 'danger',
      onOk: () => {
        message.success(`User ${user.firstName} ${user.lastName} deleted successfully`);
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingUser) {
        message.success('User updated successfully');
      } else {
        message.success('User created successfully');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <AdminGuard fallback={<div>Access denied. Admin role required.</div>}>
      <div>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">User Management</h1>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            Add User
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />

        <Modal
          title={editingUser ? 'Edit User' : 'Create User'}
          open={isModalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              isActive: true,
              emailVerified: false,
            }}
          >
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please input first name!' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please input last name!' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input email!' },
                { type: 'email', message: 'Please enter a valid email!' },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="role"
              label="Role"
              rules={[{ required: true, message: 'Please select a role!' }]}
            >
              <Input placeholder="e.g., Admin, Editor, Viewer" />
            </Form.Item>

            <Form.Item
              name="tenant"
              label="Organization"
              rules={[{ required: true, message: 'Please input organization!' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="isActive" label="Active" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Form.Item name="emailVerified" label="Email Verified" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </AdminGuard>
  );
}
