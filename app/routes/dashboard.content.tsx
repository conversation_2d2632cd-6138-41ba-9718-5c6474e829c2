/**
 * Content Management Route
 */

import React, { useState } from 'react';
import { Table, Button, Space, Tag, Modal, Form, Input, Select, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FileTextOutlined, EyeOutlined } from '@ant-design/icons';
import type { Route } from './+types/dashboard.content';
import { requirePermissions } from '../lib/rbac/guards/route-guard';
import { Can, PermissionGuard } from '../lib/rbac/components';

const { TextArea } = Input;
const { Option } = Select;

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Content Management - Admin Panel' },
    { name: 'description', content: 'Manage content with role-based permissions' },
  ];
}

export async function loader({ request }: Route.LoaderArgs) {
  // Require content read permission
  const userContext = await requirePermissions([
    { resource: 'content', actions: ['read'] }
  ])({ request } as any);
  
  // Mock content data
  const content = [
    {
      id: 'content-1',
      title: 'Welcome to RBAC System',
      type: 'Article',
      status: 'Published',
      author: '<PERSON>',
      createdAt: '2024-01-15',
      updatedAt: '2024-01-15',
    },
    {
      id: 'content-2',
      title: 'User Guide',
      type: 'Documentation',
      status: 'Draft',
      author: 'Jane Editor',
      createdAt: '2024-01-20',
      updatedAt: '2024-01-22',
    },
    {
      id: 'content-3',
      title: 'System Announcement',
      type: 'News',
      status: 'Published',
      author: 'John Admin',
      createdAt: '2024-02-01',
      updatedAt: '2024-02-01',
    },
  ];
  
  return { content, userContext };
}

export default function ContentPage({ loaderData }: Route.ComponentProps) {
  const { content } = loaderData;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingContent, setEditingContent] = useState<any>(null);
  const [form] = Form.useForm();

  const columns = [
    {
      title: 'Content',
      key: 'content',
      render: (record: any) => (
        <div className="flex items-center space-x-3">
          <FileTextOutlined className="text-gray-400" />
          <div>
            <div className="font-medium">{record.title}</div>
            <div className="text-sm text-gray-500">by {record.author}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'Article' ? 'blue' : type === 'Documentation' ? 'green' : 'orange'}>
          {type}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'Published' ? 'green' : 'orange'}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            View
          </Button>
          
          <Can resource="content" action="update">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              Edit
            </Button>
          </Can>
          
          <Can resource="content" action="delete">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            >
              Delete
            </Button>
          </Can>
        </Space>
      ),
    },
  ];

  const handleView = (content: any) => {
    message.info(`Viewing: ${content.title}`);
  };

  const handleCreate = () => {
    setEditingContent(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (content: any) => {
    setEditingContent(content);
    form.setFieldsValue(content);
    setIsModalVisible(true);
  };

  const handleDelete = (content: any) => {
    Modal.confirm({
      title: 'Delete Content',
      content: `Are you sure you want to delete "${content.title}"?`,
      okText: 'Delete',
      okType: 'danger',
      onOk: () => {
        message.success(`Content "${content.title}" deleted successfully`);
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingContent) {
        message.success('Content updated successfully');
      } else {
        message.success('Content created successfully');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Content Management</h1>
        
        <Can resource="content" action="create">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            Create Content
          </Button>
        </Can>
      </div>

      <PermissionGuard
        permissions={[{ resource: 'content', actions: ['read'] }]}
        fallback={<div>You don't have permission to view content.</div>}
      >
        <Table
          columns={columns}
          dataSource={content}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </PermissionGuard>

      <Modal
        title={editingContent ? 'Edit Content' : 'Create Content'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'Draft',
            type: 'Article',
          }}
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please input title!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label="Type"
            rules={[{ required: true, message: 'Please select type!' }]}
          >
            <Select>
              <Option value="Article">Article</Option>
              <Option value="Documentation">Documentation</Option>
              <Option value="News">News</Option>
              <Option value="Tutorial">Tutorial</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select status!' }]}
          >
            <Select>
              <Option value="Draft">Draft</Option>
              <Option value="Published">Published</Option>
              <Option value="Archived">Archived</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="content"
            label="Content"
            rules={[{ required: true, message: 'Please input content!' }]}
          >
            <TextArea rows={6} placeholder="Enter content here..." />
          </Form.Item>

          <Form.Item
            name="tags"
            label="Tags"
          >
            <Input placeholder="Enter tags separated by commas" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
