/**
 * Login Page Route
 */

import React from 'react';
import { redirect } from 'react-router';
import type { Route } from './+types/auth.login';
import { LoginForm, QuickLoginDemo } from '../lib/rbac/components/LoginForm';
import { optionalAuth } from '../lib/rbac/guards/route-guard';
import { REDIRECT_PATHS } from '../lib/rbac/constants';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Sign In - Admin Panel' },
    { name: 'description', content: 'Sign in to your account' },
  ];
}

export async function loader({ request }: Route.LoaderArgs) {
  // Check if user is already authenticated
  const userContext = await optionalAuth({ request } as any);
  
  if (userContext) {
    // Redirect to dashboard if already authenticated
    throw redirect(REDIRECT_PATHS.DEFAULT_AFTER_LOGIN);
  }

  return null;
}

export default function LoginPage() {
  const handleLoginSuccess = () => {
    // Navigation will be handled by the login form
    window.location.href = REDIRECT_PATHS.DEFAULT_AFTER_LOGIN;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Admin Panel
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Role-Based Access Control Demo
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <LoginForm onSuccess={handleLoginSuccess} />
        <QuickLoginDemo />
      </div>
    </div>
  );
}
