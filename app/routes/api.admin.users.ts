/**
 * Admin Users API Route
 * Handles user management operations (admin only)
 */

import { json } from 'react-router';
import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import { withAdmin } from '../lib/rbac/middleware/auth.middleware';

// Mock user data for demonstration
const mockUsers = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Admin',
    isActive: true,
    emailVerified: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Editor',
    isActive: true,
    emailVerified: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    firstName: 'Bob',
    lastName: 'Viewer',
    isActive: true,
    emailVerified: false,
    createdAt: new Date().toISOString(),
  },
];

/**
 * Get all users (admin only)
 */
export const loader = withAdmin(async ({ userContext }: LoaderFunctionArgs & { userContext: any }) => {
  return json({
    users: mockUsers,
    total: mockUsers.length,
    currentUser: userContext.user,
  });
});

/**
 * Create, update, or delete users (admin only)
 */
export const action = withAdmin(async ({ request, userContext }: ActionFunctionArgs & { userContext: any }) => {
  const method = request.method;
  
  switch (method) {
    case 'POST':
      // Create user
      const createData = await request.json();
      const newUser = {
        id: `user-${Date.now()}`,
        ...createData,
        isActive: true,
        emailVerified: false,
        createdAt: new Date().toISOString(),
      };
      
      return json({
        success: true,
        user: newUser,
        message: 'User created successfully',
      });

    case 'PUT':
      // Update user
      const updateData = await request.json();
      const updatedUser = {
        ...mockUsers[0], // Mock: update first user
        ...updateData,
        updatedAt: new Date().toISOString(),
      };
      
      return json({
        success: true,
        user: updatedUser,
        message: 'User updated successfully',
      });

    case 'DELETE':
      // Delete user
      const deleteData = await request.json();
      
      return json({
        success: true,
        message: `User ${deleteData.userId} deleted successfully`,
      });

    default:
      return json(
        { error: 'Method not allowed' },
        { status: 405 }
      );
  }
});
