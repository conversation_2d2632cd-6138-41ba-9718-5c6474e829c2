/**
 * Forbidden Access Page
 */

import React from 'react';
import type { Route } from './+types/auth.forbidden';
import { ForbiddenError } from '../lib/rbac/components/ErrorBoundary';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Access Denied - Admin Panel' },
    { name: 'description', content: 'Access denied' },
  ];
}

export default function ForbiddenPage() {
  return <ForbiddenError />;
}
