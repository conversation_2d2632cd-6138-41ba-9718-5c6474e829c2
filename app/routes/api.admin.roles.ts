/**
 * Admin Roles API Route
 * Handles role management operations (admin only)
 */

import { json } from 'react-router';
import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import { withAdmin } from '../lib/rbac/middleware/auth.middleware';
import { rbacService } from '../lib/rbac/services/rbac.service';

/**
 * Get all roles (admin only)
 */
export const loader = withAdmin(async ({ userContext }: LoaderFunctionArgs & { userContext: any }) => {
  try {
    const roles = await rbacService.getAllRoles();
    const systemRoles = await rbacService.getSystemRoles();
    const roleHierarchy = rbacService.getRoleHierarchy();
    
    return json({
      roles,
      systemRoles,
      roleHierarchy,
      total: roles.length,
    });
  } catch (error) {
    return json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    );
  }
});

/**
 * Create, update, or delete roles (admin only)
 */
export const action = withAdmin(async ({ request, userContext }: ActionFunctionArgs & { userContext: any }) => {
  const method = request.method;
  
  try {
    switch (method) {
      case 'POST':
        // Create role
        const createData = await request.json();
        
        // Validate required fields
        if (!createData.name || !createData.displayName) {
          return json(
            { error: 'Role name and display name are required' },
            { status: 400 }
          );
        }
        
        const newRole = {
          name: createData.name,
          displayName: createData.displayName,
          description: createData.description || '',
          level: createData.level || 999,
          permissions: createData.permissions || [],
          inheritsFrom: createData.inheritsFrom || [],
          isSystemRole: false,
        };
        
        // In a real implementation, this would save to database
        return json({
          success: true,
          role: { ...newRole, id: `role-${Date.now()}` },
          message: 'Role created successfully',
        });

      case 'PUT':
        // Update role
        const updateData = await request.json();
        const { roleId, ...updates } = updateData;
        
        if (!roleId) {
          return json(
            { error: 'Role ID is required' },
            { status: 400 }
          );
        }
        
        // In a real implementation, this would update in database
        return json({
          success: true,
          role: { id: roleId, ...updates },
          message: 'Role updated successfully',
        });

      case 'DELETE':
        // Delete role
        const deleteData = await request.json();
        const { roleId: deleteRoleId } = deleteData;
        
        if (!deleteRoleId) {
          return json(
            { error: 'Role ID is required' },
            { status: 400 }
          );
        }
        
        // Check if it's a system role
        const systemRoles = await rbacService.getSystemRoles();
        const isSystemRole = systemRoles.some(role => role.id === deleteRoleId);
        
        if (isSystemRole) {
          return json(
            { error: 'Cannot delete system roles' },
            { status: 403 }
          );
        }
        
        // In a real implementation, this would delete from database
        return json({
          success: true,
          message: 'Role deleted successfully',
        });

      default:
        return json(
          { error: 'Method not allowed' },
          { status: 405 }
        );
    }
  } catch (error) {
    console.error('Role management error:', error);
    return json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
