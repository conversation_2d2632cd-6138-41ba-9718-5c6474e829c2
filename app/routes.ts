import {
  type RouteConfig,
  index,
  route,
  layout,
} from "@react-router/dev/routes";

export default [
  // Home route
  index("routes/home.tsx"),

  // Authentication routes
  route("auth/login", "routes/auth.login.tsx"),
  route("auth/unauthorized", "routes/auth.unauthorized.tsx"),
  route("auth/forbidden", "routes/auth.forbidden.tsx"),

  // API routes
  route("api/auth/login", "routes/api.auth.login.ts"),
  route("api/auth/logout", "routes/api.auth.logout.ts"),
  route("api/auth/refresh", "routes/api.auth.refresh.ts"),
  route("api/users/profile", "routes/api.users.profile.ts"),
  route("api/roles/assign", "routes/api.roles.assign.ts"),
  route("api/admin/users", "routes/api.admin.users.ts"),
  route("api/admin/roles", "routes/api.admin.roles.ts"),

  // Dashboard routes (protected)
  layout("routes/dashboard.tsx", [
    index("routes/dashboard._index.tsx"),
    route("users", "routes/dashboard.users.tsx"),
    route("content", "routes/dashboard.content.tsx"),
  ]),
] satisfies RouteConfig;
