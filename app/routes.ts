/**
 * Route Configuration - Flat Structure
 * Flat routes for better performance and maintainability in large applications
 */

import { type RouteConfig, index, route } from "@react-router/dev/routes";
import { APP_CONFIG } from "./config/app";

export default [
  // Root route
  index("routes/home.tsx"),

  // Authentication routes (public)
  route("auth/login", "routes/auth.login.tsx"),
  route("auth/register", "routes/auth.register.tsx"),
  route("auth/forgot-password", "routes/auth.forgot-password.tsx"),
  route("auth/reset-password", "routes/auth.reset-password.tsx"),
  route("auth/verify-email", "routes/auth.verify-email.tsx"),
  route("auth/unauthorized", "routes/auth.unauthorized.tsx"),
  route("auth/forbidden", "routes/auth.forbidden.tsx"),

  // Dashboard (protected)
  route("dashboard", "routes/dashboard.tsx"),

  // User Management (protected)
  route("admin/users", "routes/admin.users.tsx"),
  route("admin/users/create", "routes/admin.users.create.tsx"),
  route("admin/users/detail/:id", "routes/admin.users.detail.$id.tsx"),
  route("admin/users/edit/:id", "routes/admin.users.edit.$id.tsx"),
  route(
    "admin/users/permissions/:id",
    "routes/admin.users.permissions.$id.tsx"
  ),

  // Role Management (protected)
  route("admin/roles", "routes/admin.roles.tsx"),
  route("admin/roles/create", "routes/admin.roles.create.tsx"),
  route("admin/roles/detail/:id", "routes/admin.roles.detail.$id.tsx"),
  route("admin/roles/edit/:id", "routes/admin.roles.edit.$id.tsx"),

  // Permission Management (protected)
  route("admin/permissions", "routes/admin.permissions.tsx"),
  route("admin/permissions/create", "routes/admin.permissions.create.tsx"),
  route(
    "admin/permissions/detail/:id",
    "routes/admin.permissions.detail.$id.tsx"
  ),

  // Settings (protected)
  route("admin/settings", "routes/admin.settings.tsx"),
  route("admin/settings/general", "routes/admin.settings.general.tsx"),
  route("admin/settings/security", "routes/admin.settings.security.tsx"),
  route("admin/settings/email", "routes/admin.settings.email.tsx"),
  route(
    "admin/settings/integrations",
    "routes/admin.settings.integrations.tsx"
  ),

  // Audit Logs (protected)
  route("admin/audit", "routes/admin.audit.tsx"),
  route("admin/audit/detail/:id", "routes/admin.audit.detail.$id.tsx"),
  route("admin/audit/export", "routes/admin.audit.export.tsx"),

  // Translation Management (protected)
  route("admin/translations", "routes/admin.translations.tsx"),
  route(
    "admin/translations/namespace/:namespace",
    "routes/admin.translations.namespace.$namespace.tsx"
  ),
  route("admin/translations/import", "routes/admin.translations.import.tsx"),
  route("admin/translations/export", "routes/admin.translations.export.tsx"),

  // Super Admin routes (obfuscated paths)
  route("sa", "routes/sa.tsx"),
  route("sa/system", "routes/sa.system.tsx"),
  route("sa/security", "routes/sa.security.tsx"),
  route("sa/monitoring", "routes/sa.monitoring.tsx"),
  route("sa/database", "routes/sa.database.tsx"),
  route("sa/logs", "routes/sa.logs.tsx"),

  // Content Management (protected)
  route("content", "routes/content.tsx"),
  route("content/pages", "routes/content.pages.tsx"),
  route("content/pages/create", "routes/content.pages.create.tsx"),
  route("content/pages/detail/:id", "routes/content.pages.detail.$id.tsx"),
  route("content/pages/edit/:id", "routes/content.pages.edit.$id.tsx"),
  route("content/media", "routes/content.media.tsx"),
  route("content/media/upload", "routes/content.media.upload.tsx"),
  route("content/categories", "routes/content.categories.tsx"),
  route("content/tags", "routes/content.tags.tsx"),

  // Reports (protected)
  route("reports", "routes/reports.tsx"),
  route("reports/analytics", "routes/reports.analytics.tsx"),
  route("reports/users", "routes/reports.users.tsx"),
  route("reports/content", "routes/reports.content.tsx"),
  route("reports/security", "routes/reports.security.tsx"),
  route("reports/performance", "routes/reports.performance.tsx"),
  route("reports/exports", "routes/reports.exports.tsx"),

  // Profile Management (protected)
  route("profile", "routes/profile.tsx"),
  route("profile/edit", "routes/profile.edit.tsx"),
  route("profile/security", "routes/profile.security.tsx"),
  route("profile/preferences", "routes/profile.preferences.tsx"),
  route("profile/sessions", "routes/profile.sessions.tsx"),

  // Notifications (protected)
  route("notifications", "routes/notifications.tsx"),
  route("notifications/detail/:id", "routes/notifications.detail.$id.tsx"),
  route("notifications/settings", "routes/notifications.settings.tsx"),

  // API routes
  route("api/auth/login", "routes/api.auth.login.ts"),
  route("api/auth/logout", "routes/api.auth.logout.ts"),
  route("api/auth/refresh", "routes/api.auth.refresh.ts"),
  route("api/auth/me", "routes/api.auth.me.ts"),
  route("api/auth/register", "routes/api.auth.register.ts"),
  route("api/auth/forgot-password", "routes/api.auth.forgot-password.ts"),
  route("api/auth/reset-password", "routes/api.auth.reset-password.ts"),

  // User API routes
  route("api/users", "routes/api.users.ts"),
  route("api/users/:id", "routes/api.users.$id.ts"),
  route("api/users/:id/permissions", "routes/api.users.$id.permissions.ts"),
  route("api/users/:id/roles", "routes/api.users.$id.roles.ts"),
  route("api/users/profile", "routes/api.users.profile.ts"),

  // Admin API routes
  route("api/admin/users", "routes/api.admin.users.ts"),
  route("api/admin/roles", "routes/api.admin.roles.ts"),
  route("api/admin/permissions", "routes/api.admin.permissions.ts"),
  route("api/admin/settings", "routes/api.admin.settings.ts"),
  route("api/admin/audit", "routes/api.admin.audit.ts"),
  route("api/admin/system", "routes/api.admin.system.ts"),
  route("api/roles/assign", "routes/api.roles.assign.ts"),

  // Translation API routes
  route("api/translations", "routes/api.translations.ts"),
  route("api/translations/:namespace", "routes/api.translations.$namespace.ts"),
  route("api/translations/import", "routes/api.translations.import.ts"),
  route("api/translations/export", "routes/api.translations.export.ts"),

  // Content API routes
  route("api/content/pages", "routes/api.content.pages.ts"),
  route("api/content/media", "routes/api.content.media.ts"),
  route("api/content/upload", "routes/api.content.upload.ts"),

  // Reports API routes
  route("api/reports/analytics", "routes/api.reports.analytics.ts"),
  route("api/reports/users", "routes/api.reports.users.ts"),
  route("api/reports/export", "routes/api.reports.export.ts"),

  // Notification API routes
  route("api/notifications", "routes/api.notifications.ts"),
  route("api/notifications/:id", "routes/api.notifications.$id.ts"),
  route("api/notifications/mark-read", "routes/api.notifications.mark-read.ts"),

  // Health check and monitoring
  route("api/health", "routes/api.health.ts"),
  route("api/metrics", "routes/api.metrics.ts"),

  // Error routes
  route("404", "routes/404.tsx"),
  route("500", "routes/500.tsx"),
  route("offline", "routes/offline.tsx"),
] satisfies RouteConfig;
