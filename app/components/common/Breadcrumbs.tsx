/**
 * Breadcrumbs Component
 * Dynamic breadcrumb navigation based on current route
 */

import React from 'react';
import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router';
import { HomeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

export function Breadcrumbs() {
  const location = useLocation();
  const { t } = useTranslation();

  const pathSegments = location.pathname.split('/').filter(Boolean);
  
  // Create breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    {
      title: <HomeOutlined />,
      path: '/',
    },
  ];

  let currentPath = '';
  
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Skip certain segments or transform them
    if (segment === 'admin') {
      return; // Skip 'admin' in breadcrumbs
    }
    
    const isLast = index === pathSegments.length - 1;
    const title = getBreadcrumbTitle(segment, currentPath);
    
    breadcrumbItems.push({
      title: isLast ? title : <Link to={currentPath}>{title}</Link>,
      path: currentPath,
    });
  });

  return (
    <Breadcrumb
      items={breadcrumbItems}
      className="text-gray-600 dark:text-gray-300"
    />
  );
}

function getBreadcrumbTitle(segment: string, path: string): string {
  // Map of path segments to display names
  const segmentMap: Record<string, string> = {
    dashboard: 'Dashboard',
    users: 'Users',
    settings: 'Settings',
    profile: 'Profile',
    content: 'Content',
    translations: 'Translations',
    reports: 'Reports',
    audit: 'Audit Logs',
    security: 'Security',
    'account-settings': 'Account Settings',
    'notification-settings': 'Notifications',
    'security-settings': 'Security Settings',
  };

  // Check for dynamic segments (like IDs)
  if (/^[a-f0-9-]{36}$/.test(segment)) {
    return 'Details'; // UUID pattern
  }
  
  if (/^\d+$/.test(segment)) {
    return `#${segment}`; // Numeric ID
  }

  return segmentMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}
