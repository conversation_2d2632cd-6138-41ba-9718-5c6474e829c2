/**
 * Theme Toggle Component
 * Allows users to switch between light and dark themes
 */

import React from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '../../store';
import { toggleTheme } from '../../store/slices/themeSlice';

export function ThemeToggle() {
  const dispatch = useDispatch();
  const { mode } = useSelector((state: RootState) => state.theme);

  const handleToggle = () => {
    dispatch(toggleTheme());
    
    // Apply theme to document
    const isDark = mode === 'light'; // Will be dark after toggle
    document.documentElement.classList.toggle('dark', isDark);
  };

  return (
    <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} theme`}>
      <Button
        type="text"
        icon={mode === 'light' ? <MoonOutlined /> : <SunOutlined />}
        onClick={handleToggle}
        className="text-gray-600 dark:text-gray-300"
      />
    </Tooltip>
  );
}
