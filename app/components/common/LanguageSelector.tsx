/**
 * Language Selector Component
 * Allows users to switch between different languages
 */

import React from 'react';
import { Select, Button, Dropdown } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { APP_CONFIG } from '../../config/app';
import type { MenuProps } from 'antd';

const { Option } = Select;

export function LanguageSelector() {
  const { i18n, t } = useTranslation();

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    
    // Store preference
    localStorage.setItem('preferred_language', languageCode);
    
    // Update document language
    document.documentElement.lang = languageCode;
  };

  const currentLanguage = APP_CONFIG.i18n.supportedLanguages.find(
    lang => lang.code === i18n.language
  ) || APP_CONFIG.i18n.supportedLanguages[0];

  const menuItems: MenuProps['items'] = APP_CONFIG.i18n.supportedLanguages.map(language => ({
    key: language.code,
    label: (
      <div className="flex items-center space-x-2 px-2 py-1">
        <span className="text-lg">{language.flag}</span>
        <div>
          <div className="font-medium">{language.name}</div>
          <div className="text-xs text-gray-500">{language.nativeName}</div>
        </div>
      </div>
    ),
    onClick: () => handleLanguageChange(language.code),
  }));

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Button
        type="text"
        icon={<GlobalOutlined />}
        className="text-gray-600 dark:text-gray-300 flex items-center space-x-1"
      >
        <span className="hidden sm:inline text-sm">
          {currentLanguage.flag} {currentLanguage.code.toUpperCase()}
        </span>
      </Button>
    </Dropdown>
  );
}
