/**
 * User Profile Dropdown Component
 * User menu with profile options and logout
 */

import React from 'react';
import { Avatar, Dropdown, Typography, Divider, Space } from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  ProfileOutlined,
  SecurityScanOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router';
import type { MenuProps } from 'antd';

const { Text } = Typography;

interface UserProfileDropdownProps {
  user: User | null;
  onLogout: () => void;
}

export function UserProfileDropdown({ user, onLogout }: UserProfileDropdownProps) {
  if (!user) return null;

  const menuItems: MenuProps['items'] = [
    {
      key: 'profile-info',
      label: (
        <div className="px-2 py-1">
          <div className="flex items-center space-x-3">
            <Avatar
              size={40}
              src={user.avatar}
              icon={<UserOutlined />}
            />
            <div>
              <Text strong className="block">
                {user.firstName} {user.lastName}
              </Text>
              <Text type="secondary" className="text-sm">
                {user.email}
              </Text>
              <div className="mt-1">
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {user.role.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: <Link to="/admin/profile">My Profile</Link>,
    },
    {
      key: 'account-settings',
      icon: <SettingOutlined />,
      label: <Link to="/admin/account-settings">Account Settings</Link>,
    },
    {
      key: 'notifications',
      icon: <BellOutlined />,
      label: <Link to="/admin/notification-settings">Notifications</Link>,
    },
    {
      key: 'security',
      icon: <SecurityScanOutlined />,
      label: <Link to="/admin/security-settings">Security</Link>,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: onLogout,
      className: 'text-red-600 hover:text-red-700 hover:bg-red-50',
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg px-2 py-1">
        <Avatar
          size="small"
          src={user.avatar}
          icon={<UserOutlined />}
        />
        <div className="hidden sm:block">
          <Text className="text-sm font-medium">
            {user.firstName} {user.lastName}
          </Text>
        </div>
      </div>
    </Dropdown>
  );
}
