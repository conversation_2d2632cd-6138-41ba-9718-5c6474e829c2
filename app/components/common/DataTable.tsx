/**
 * Advanced Data Table Component
 * Feature-rich table with inline editing, sorting, filtering, and bulk operations
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Table,
  Input,
  Button,
  Space,
  Dropdown,
  Checkbox,
  Tooltip,
  Popconfirm,
  Tag,
  Select,
  DatePicker,
  InputNumber,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  CloseOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { TableProps, ColumnType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { useTranslation } from 'react-i18next';

interface DataTableColumn<T> extends Omit<ColumnType<T>, 'render'> {
  editable?: boolean;
  editType?: 'text' | 'number' | 'select' | 'date' | 'textarea';
  editOptions?: { label: string; value: any }[];
  searchable?: boolean;
  filterable?: boolean;
  exportable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

interface DataTableProps<T> extends Omit<TableProps<T>, 'columns'> {
  columns: DataTableColumn<T>[];
  data: T[];
  loading?: boolean;
  onEdit?: (record: T, field: string, value: any) => Promise<void>;
  onDelete?: (record: T) => Promise<void>;
  onBulkDelete?: (records: T[]) => Promise<void>;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => Promise<void>;
  onRefresh?: () => Promise<void>;
  searchable?: boolean;
  filterable?: boolean;
  exportable?: boolean;
  bulkActions?: boolean;
  inlineEditing?: boolean;
  virtualScrolling?: boolean;
  rowKey?: string | ((record: T) => string);
}

export function DataTable<T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  onEdit,
  onDelete,
  onBulkDelete,
  onExport,
  onRefresh,
  searchable = true,
  filterable = true,
  exportable = true,
  bulkActions = true,
  inlineEditing = true,
  virtualScrolling = false,
  rowKey = 'id',
  ...tableProps
}: DataTableProps<T>) {
  const { t } = useTranslation();
  
  const [searchText, setSearchText] = useState('');
  const [editingKey, setEditingKey] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [filters, setFilters] = useState<Record<string, any>>({});

  // Get row key value
  const getRowKey = useCallback((record: T): string => {
    return typeof rowKey === 'function' ? rowKey(record) : record[rowKey];
  }, [rowKey]);

  // Check if row is being edited
  const isEditing = useCallback((record: T) => {
    return getRowKey(record) === editingKey;
  }, [editingKey, getRowKey]);

  // Start editing
  const startEdit = useCallback((record: T) => {
    setEditingKey(getRowKey(record));
  }, [getRowKey]);

  // Cancel editing
  const cancelEdit = useCallback(() => {
    setEditingKey('');
  }, []);

  // Save edit
  const saveEdit = useCallback(async (record: T, field: string, value: any) => {
    if (onEdit) {
      try {
        await onEdit(record, field, value);
        setEditingKey('');
      } catch (error) {
        console.error('Edit failed:', error);
      }
    }
  }, [onEdit]);

  // Handle delete
  const handleDelete = useCallback(async (record: T) => {
    if (onDelete) {
      try {
        await onDelete(record);
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  }, [onDelete]);

  // Handle bulk delete
  const handleBulkDelete = useCallback(async () => {
    if (onBulkDelete && selectedRowKeys.length > 0) {
      const selectedRecords = data.filter(record => 
        selectedRowKeys.includes(getRowKey(record))
      );
      try {
        await onBulkDelete(selectedRecords);
        setSelectedRowKeys([]);
      } catch (error) {
        console.error('Bulk delete failed:', error);
      }
    }
  }, [onBulkDelete, selectedRowKeys, data, getRowKey]);

  // Filter data based on search and filters
  const filteredData = useMemo(() => {
    let filtered = [...data];

    // Apply search filter
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(record =>
        columns.some(column => {
          if (!column.searchable) return false;
          const value = record[column.dataIndex as string];
          return String(value).toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        filtered = filtered.filter(record => {
          const recordValue = record[key];
          if (Array.isArray(value)) {
            return value.includes(recordValue);
          }
          return recordValue === value;
        });
      }
    });

    return filtered;
  }, [data, searchText, filters, columns]);

  // Create editable cell
  const EditableCell: React.FC<{
    editing: boolean;
    dataIndex: string;
    title: string;
    editType: DataTableColumn<T>['editType'];
    editOptions?: DataTableColumn<T>['editOptions'];
    record: T;
    children: React.ReactNode;
  }> = ({
    editing,
    dataIndex,
    title,
    editType = 'text',
    editOptions,
    record,
    children,
    ...restProps
  }) => {
    const [value, setValue] = useState(record[dataIndex]);

    const handleSave = () => {
      saveEdit(record, dataIndex, value);
    };

    const handleCancel = () => {
      setValue(record[dataIndex]);
      cancelEdit();
    };

    if (!editing) {
      return <td {...restProps}>{children}</td>;
    }

    let inputNode: React.ReactNode;

    switch (editType) {
      case 'number':
        inputNode = (
          <InputNumber
            value={value}
            onChange={setValue}
            onPressEnter={handleSave}
            style={{ width: '100%' }}
          />
        );
        break;
      case 'select':
        inputNode = (
          <Select
            value={value}
            onChange={setValue}
            options={editOptions}
            style={{ width: '100%' }}
          />
        );
        break;
      case 'date':
        inputNode = (
          <DatePicker
            value={value}
            onChange={setValue}
            style={{ width: '100%' }}
          />
        );
        break;
      case 'textarea':
        inputNode = (
          <Input.TextArea
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onPressEnter={handleSave}
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        );
        break;
      default:
        inputNode = (
          <Input
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onPressEnter={handleSave}
          />
        );
    }

    return (
      <td {...restProps}>
        <div className="flex items-center space-x-2">
          <div className="flex-1">{inputNode}</div>
          <Space size="small">
            <Button
              type="text"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleSave}
            />
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleCancel}
            />
          </Space>
        </div>
      </td>
    );
  };

  // Enhanced columns with editing capabilities
  const enhancedColumns = useMemo(() => {
    const cols = columns.map(column => ({
      ...column,
      onCell: (record: T) => ({
        record,
        editing: isEditing(record),
        dataIndex: column.dataIndex,
        title: column.title,
        editType: column.editType,
        editOptions: column.editOptions,
      }),
    }));

    // Add actions column if inline editing or delete is enabled
    if (inlineEditing || onDelete) {
      cols.push({
        title: 'Actions',
        key: 'actions',
        width: 120,
        render: (_, record: T) => {
          const editing = isEditing(record);
          
          if (editing) {
            return null; // Actions are handled in EditableCell
          }

          const actions = [];

          if (inlineEditing && onEdit) {
            actions.push(
              <Tooltip title="Edit" key="edit">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => startEdit(record)}
                />
              </Tooltip>
            );
          }

          if (onDelete) {
            actions.push(
              <Popconfirm
                title="Are you sure you want to delete this item?"
                onConfirm={() => handleDelete(record)}
                key="delete"
              >
                <Tooltip title="Delete">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                  />
                </Tooltip>
              </Popconfirm>
            );
          }

          return <Space size="small">{actions}</Space>;
        },
      });
    }

    return cols;
  }, [columns, isEditing, inlineEditing, onEdit, onDelete, startEdit, handleDelete]);

  // Export menu items
  const exportMenuItems: MenuProps['items'] = [
    {
      key: 'csv',
      label: 'Export as CSV',
      onClick: () => onExport?.('csv'),
    },
    {
      key: 'excel',
      label: 'Export as Excel',
      onClick: () => onExport?.('excel'),
    },
    {
      key: 'pdf',
      label: 'Export as PDF',
      onClick: () => onExport?.('pdf'),
    },
  ];

  // Row selection configuration
  const rowSelection = bulkActions ? {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  } : undefined;

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {searchable && (
            <Input
              placeholder="Search..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
          )}
          
          {selectedRowKeys.length > 0 && (
            <div className="flex items-center space-x-2">
              <Tag color="blue">{selectedRowKeys.length} selected</Tag>
              {onBulkDelete && (
                <Popconfirm
                  title={`Delete ${selectedRowKeys.length} items?`}
                  onConfirm={handleBulkDelete}
                >
                  <Button size="small" danger>
                    Delete Selected
                  </Button>
                </Popconfirm>
              )}
            </div>
          )}
        </div>

        <Space>
          {onRefresh && (
            <Tooltip title="Refresh">
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              />
            </Tooltip>
          )}
          
          {exportable && onExport && (
            <Dropdown menu={{ items: exportMenuItems }}>
              <Button icon={<ExportOutlined />}>
                Export
              </Button>
            </Dropdown>
          )}
        </Space>
      </div>

      {/* Table */}
      <Table
        {...tableProps}
        columns={enhancedColumns}
        dataSource={filteredData}
        loading={loading}
        rowKey={rowKey}
        rowSelection={rowSelection}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        scroll={virtualScrolling ? { y: 400 } : undefined}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
          ...tableProps.pagination,
        }}
      />
    </div>
  );
}
