/**
 * Notification Center Component
 * Displays and manages user notifications
 */

import React, { useState } from 'react';
import {
  Button,
  Dropdown,
  List,
  Badge,
  Typography,
  Empty,
  Divider,
  Space,
  Tooltip,
} from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '../../store';
import {
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearAll,
} from '../../store/slices/notificationSlice';

const { Text } = Typography;

export function NotificationCenter() {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();
  
  const { items, unreadCount } = useSelector((state: RootState) => state.notifications);

  const handleMarkAsRead = (id: string) => {
    dispatch(markAsRead(id));
  };

  const handleRemove = (id: string) => {
    dispatch(removeNotification(id));
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  const handleClearAll = () => {
    dispatch(clearAll());
    setOpen(false);
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconMap = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
    };
    return iconMap[type] || 'ℹ️';
  };

  const getNotificationColor = (type: Notification['type']) => {
    const colorMap = {
      success: '#52c41a',
      error: '#ff4d4f',
      warning: '#faad14',
      info: '#1890ff',
    };
    return colorMap[type] || '#1890ff';
  };

  const notificationContent = (
    <div className="w-80 max-h-96 overflow-hidden">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <Text strong>Notifications</Text>
          {items.length > 0 && (
            <Space size="small">
              {unreadCount > 0 && (
                <Tooltip title="Mark all as read">
                  <Button
                    type="text"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={handleMarkAllAsRead}
                  />
                </Tooltip>
              )}
              <Tooltip title="Clear all">
                <Button
                  type="text"
                  size="small"
                  icon={<ClearOutlined />}
                  onClick={handleClearAll}
                />
              </Tooltip>
            </Space>
          )}
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-80 overflow-y-auto">
        {items.length === 0 ? (
          <div className="p-4">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="No notifications"
            />
          </div>
        ) : (
          <List
            dataSource={items}
            renderItem={(notification) => (
              <List.Item
                className={`px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-800 ${
                  !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                actions={[
                  <Tooltip title="Mark as read">
                    <Button
                      type="text"
                      size="small"
                      icon={<CheckOutlined />}
                      onClick={() => handleMarkAsRead(notification.id)}
                      disabled={notification.read}
                    />
                  </Tooltip>,
                  <Tooltip title="Remove">
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemove(notification.id)}
                    />
                  </Tooltip>,
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <span
                      style={{ color: getNotificationColor(notification.type) }}
                      className="text-lg"
                    >
                      {getNotificationIcon(notification.type)}
                    </span>
                  }
                  title={
                    <div className="flex items-center space-x-2">
                      <Text strong={!notification.read} className="text-sm">
                        {notification.title}
                      </Text>
                      {!notification.read && (
                        <Badge
                          status="processing"
                          className="flex-shrink-0"
                        />
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <Text className="text-xs text-gray-600 dark:text-gray-400">
                        {notification.message}
                      </Text>
                      <br />
                      <Text className="text-xs text-gray-400">
                        {new Date(notification.createdAt).toLocaleString()}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>

      {/* Footer */}
      {items.length > 5 && (
        <>
          <Divider className="my-0" />
          <div className="p-2 text-center">
            <Button type="link" size="small">
              View all notifications
            </Button>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      overlay={notificationContent}
      trigger={['click']}
      placement="bottomRight"
      open={open}
      onOpenChange={setOpen}
    >
      <Button
        type="text"
        icon={<BellOutlined />}
        className="text-gray-600 dark:text-gray-300"
      />
    </Dropdown>
  );
}
