/**
 * Admin Layout Component
 * Main layout for the admin panel with responsive sidebar and header
 */

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge, Drawer, Grid } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  FileTextOutlined,
  TeamOutlined,
  Bar<PERSON>hartOutlined,
  TranslationOutlined,
} from '@ant-design/icons';
import { Link, useLocation, Outlet } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '../../store';
import { logoutAsync } from '../../store/slices/authSlice';
import { toggleTheme } from '../../store/slices/themeSlice';
import { APP_CONFIG } from '../../config/app';
import { ThemeToggle } from '../common/ThemeToggle';
import { NotificationCenter } from '../common/NotificationCenter';
import { UserProfileDropdown } from '../common/UserProfileDropdown';
import { LanguageSelector } from '../common/LanguageSelector';
import { Breadcrumbs } from '../common/Breadcrumbs';

const { Header, Sider, Content } = Layout;
const { useBreakpoint } = Grid;

interface AdminLayoutProps {
  children?: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  
  const location = useLocation();
  const dispatch = useDispatch();
  const screens = useBreakpoint();
  
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const { unreadCount } = useSelector((state: RootState) => state.notifications);
  const theme = useSelector((state: RootState) => state.theme);

  const isMobile = !screens.md;

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  const handleLogout = () => {
    dispatch(logoutAsync());
  };

  const menuItems = [
    {
      key: APP_CONFIG.routes.dashboard,
      icon: <DashboardOutlined />,
      label: <Link to={APP_CONFIG.routes.dashboard}>Dashboard</Link>,
    },
    {
      key: APP_CONFIG.routes.users,
      icon: <TeamOutlined />,
      label: <Link to={APP_CONFIG.routes.users}>Users</Link>,
      // Show only for admin users
      style: { display: user?.role === 'admin' || user?.role === 'super_admin' ? 'block' : 'none' },
    },
    {
      key: '/admin/content',
      icon: <FileTextOutlined />,
      label: <Link to="/admin/content">Content</Link>,
    },
    {
      key: '/admin/translations',
      icon: <TranslationOutlined />,
      label: <Link to="/admin/translations">Translations</Link>,
    },
    {
      key: '/admin/reports',
      icon: <BarChartOutlined />,
      label: <Link to="/admin/reports">Reports</Link>,
    },
    {
      key: APP_CONFIG.routes.settings,
      icon: <SettingOutlined />,
      label: <Link to={APP_CONFIG.routes.settings}>Settings</Link>,
    },
    // Super admin only
    ...(user?.role === 'super_admin' ? [
      {
        key: APP_CONFIG.routes.superAdmin,
        icon: <SecurityScanOutlined />,
        label: <Link to={APP_CONFIG.routes.superAdmin}>System Admin</Link>,
      },
      {
        key: APP_CONFIG.routes.audit,
        icon: <SecurityScanOutlined />,
        label: <Link to={APP_CONFIG.routes.audit}>Audit Logs</Link>,
      },
    ] : []),
  ];

  const headerActions = (
    <div className="flex items-center space-x-4">
      <LanguageSelector />
      <ThemeToggle />
      
      <Badge count={unreadCount} size="small">
        <NotificationCenter />
      </Badge>
      
      <UserProfileDropdown user={user} onLogout={handleLogout} />
    </div>
  );

  const sidebarContent = (
    <div className="h-full flex flex-col">
      {/* Logo */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">A</span>
          </div>
          {!collapsed && (
            <span className="ml-3 text-lg font-semibold text-gray-900 dark:text-white">
              {APP_CONFIG.name}
            </span>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          className="border-none"
          inlineCollapsed={collapsed}
        />
      </div>

      {/* User Info */}
      {!collapsed && user && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Avatar
              size="small"
              src={user.avatar}
              icon={<UserOutlined />}
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {user.role}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  if (!isAuthenticated) {
    return null; // This should be handled by route guards
  }

  return (
    <Layout className="min-h-screen">
      {/* Desktop Sidebar */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={250}
          collapsedWidth={80}
          className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"
        >
          {sidebarContent}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title={APP_CONFIG.name}
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={250}
        >
          {sidebarContent}
        </Drawer>
      )}

      <Layout>
        {/* Header */}
        <Header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => {
                if (isMobile) {
                  setMobileDrawerVisible(true);
                } else {
                  setCollapsed(!collapsed);
                }
              }}
              className="text-gray-600 dark:text-gray-300"
            />
            
            <Breadcrumbs />
          </div>

          {headerActions}
        </Header>

        {/* Main Content */}
        <Content className="bg-gray-50 dark:bg-gray-900 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {children || <Outlet />}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}

export default AdminLayout;
