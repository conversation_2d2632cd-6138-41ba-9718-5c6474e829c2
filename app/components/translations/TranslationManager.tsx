/**
 * Translation Management Interface
 * Advanced translation management with inline editing and language switching
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Table,
  Input,
  Button,
  Space,
  Select,
  Tag,
  Modal,
  Form,
  Card,
  Tabs,
  Progress,
  Tooltip,
  Switch,
  Popconfirm,
} from 'antd';
import {
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  TranslationOutlined,
  GlobalOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { APP_CONFIG } from '../../config/app';
import { useHttp } from '../../hooks/useHttp';
import { HasPermission } from '../../lib/rbac/advanced/PermissionRenderer';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface TranslationEntry {
  id: string;
  key: string;
  namespace: string;
  values: Record<string, string>;
  status: Record<string, 'complete' | 'incomplete' | 'needs_review'>;
  lastModified: string;
  modifiedBy: string;
}

interface TranslationManagerProps {
  namespace?: string;
  editable?: boolean;
}

export function TranslationManager({ 
  namespace = 'common',
  editable = true 
}: TranslationManagerProps) {
  const { t, i18n } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const [editingKey, setEditingKey] = useState<string>('');
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showOnlyIncomplete, setShowOnlyIncomplete] = useState(false);

  // Fetch translations
  const {
    data: translations,
    loading,
    execute: fetchTranslations,
  } = useHttp<TranslationEntry[]>('/translations', 'POST', {
    immediate: true,
  });

  // Update translation
  const {
    loading: updating,
    execute: updateTranslation,
  } = useHttp('/translations/update', 'PUT', {
    showSuccessNotification: true,
    successMessage: 'Translation updated successfully',
  });

  // Mock data for demonstration
  const mockTranslations: TranslationEntry[] = [
    {
      id: '1',
      key: 'common.welcome',
      namespace: 'common',
      values: {
        en: 'Welcome',
        es: 'Bienvenido',
        fr: 'Bienvenue',
        de: 'Willkommen',
      },
      status: {
        en: 'complete',
        es: 'complete',
        fr: 'complete',
        de: 'incomplete',
      },
      lastModified: '2024-01-15T10:30:00Z',
      modifiedBy: 'admin',
    },
    {
      id: '2',
      key: 'common.save',
      namespace: 'common',
      values: {
        en: 'Save',
        es: 'Guardar',
        fr: 'Enregistrer',
        de: '',
      },
      status: {
        en: 'complete',
        es: 'complete',
        fr: 'needs_review',
        de: 'incomplete',
      },
      lastModified: '2024-01-14T15:45:00Z',
      modifiedBy: 'translator',
    },
    {
      id: '3',
      key: 'dashboard.title',
      namespace: 'dashboard',
      values: {
        en: 'Dashboard',
        es: 'Panel de Control',
        fr: 'Tableau de Bord',
        de: 'Dashboard',
      },
      status: {
        en: 'complete',
        es: 'complete',
        fr: 'complete',
        de: 'complete',
      },
      lastModified: '2024-01-13T09:15:00Z',
      modifiedBy: 'admin',
    },
  ];

  const currentTranslations = translations || mockTranslations;

  const isEditing = useCallback((record: TranslationEntry) => {
    return editingKey === record.id;
  }, [editingKey]);

  const startEdit = useCallback((record: TranslationEntry) => {
    setEditingKey(record.id);
  }, []);

  const cancelEdit = useCallback(() => {
    setEditingKey('');
  }, []);

  const saveEdit = useCallback(async (record: TranslationEntry, language: string, value: string) => {
    try {
      await updateTranslation({
        id: record.id,
        language,
        value,
      });
      
      // Update local data
      const updatedTranslations = currentTranslations.map(t => 
        t.id === record.id 
          ? {
              ...t,
              values: { ...t.values, [language]: value },
              status: { ...t.status, [language]: value ? 'complete' : 'incomplete' },
              lastModified: new Date().toISOString(),
            }
          : t
      );
      
      setEditingKey('');
    } catch (error) {
      console.error('Failed to update translation:', error);
    }
  }, [updateTranslation, currentTranslations]);

  const filteredTranslations = useMemo(() => {
    let filtered = currentTranslations;

    // Search filter
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(t => 
        t.key.toLowerCase().includes(searchLower) ||
        Object.values(t.values).some(value => 
          value.toLowerCase().includes(searchLower)
        )
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(t => 
        t.status[selectedLanguage] === statusFilter
      );
    }

    // Show only incomplete
    if (showOnlyIncomplete) {
      filtered = filtered.filter(t => 
        t.status[selectedLanguage] === 'incomplete' || !t.values[selectedLanguage]
      );
    }

    return filtered;
  }, [currentTranslations, searchText, statusFilter, selectedLanguage, showOnlyIncomplete]);

  const getStatusColor = (status: TranslationEntry['status'][string]) => {
    switch (status) {
      case 'complete': return 'green';
      case 'needs_review': return 'orange';
      case 'incomplete': return 'red';
      default: return 'default';
    }
  };

  const getCompletionPercentage = (translation: TranslationEntry) => {
    const languages = APP_CONFIG.i18n.supportedLanguages.map(l => l.code);
    const completed = languages.filter(lang => 
      translation.values[lang] && translation.status[lang] === 'complete'
    ).length;
    return Math.round((completed / languages.length) * 100);
  };

  const EditableCell: React.FC<{
    editing: boolean;
    record: TranslationEntry;
    language: string;
    children: React.ReactNode;
  }> = ({ editing, record, language, children, ...restProps }) => {
    const [value, setValue] = useState(record.values[language] || '');

    const handleSave = () => {
      saveEdit(record, language, value);
    };

    const handleCancel = () => {
      setValue(record.values[language] || '');
      cancelEdit();
    };

    if (!editing) {
      return <td {...restProps}>{children}</td>;
    }

    return (
      <td {...restProps}>
        <div className="flex items-center space-x-2">
          <TextArea
            value={value}
            onChange={(e) => setValue(e.target.value)}
            autoSize={{ minRows: 1, maxRows: 4 }}
            className="flex-1"
          />
          <Space direction="vertical" size="small">
            <Button
              type="text"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={updating}
            />
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleCancel}
            />
          </Space>
        </div>
      </td>
    );
  };

  const columns = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      width: 200,
      fixed: 'left' as const,
      render: (key: string, record: TranslationEntry) => (
        <div>
          <div className="font-mono text-sm">{key}</div>
          <div className="text-xs text-gray-500">{record.namespace}</div>
        </div>
      ),
    },
    {
      title: 'Completion',
      key: 'completion',
      width: 120,
      render: (record: TranslationEntry) => {
        const percentage = getCompletionPercentage(record);
        return (
          <div>
            <Progress
              percent={percentage}
              size="small"
              status={percentage === 100 ? 'success' : 'normal'}
            />
            <div className="text-xs text-gray-500 mt-1">
              {percentage}% complete
            </div>
          </div>
        );
      },
    },
    ...APP_CONFIG.i18n.supportedLanguages.map(lang => ({
      title: (
        <div className="flex items-center space-x-2">
          <span>{lang.flag}</span>
          <span>{lang.code.toUpperCase()}</span>
        </div>
      ),
      key: lang.code,
      width: 250,
      onCell: (record: TranslationEntry) => ({
        record,
        editing: isEditing(record),
        language: lang.code,
      }),
      render: (record: TranslationEntry) => {
        const value = record.values[lang.code] || '';
        const status = record.status[lang.code] || 'incomplete';
        const editing = isEditing(record);

        if (editing) {
          return null; // Handled by EditableCell
        }

        return (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className={value ? '' : 'text-gray-400 italic'}>
                {value || 'Not translated'}
              </span>
              {editable && (
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => startEdit(record)}
                />
              )}
            </div>
            <Tag color={getStatusColor(status)} size="small">
              {status.replace('_', ' ')}
            </Tag>
          </div>
        );
      },
    })),
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (record: TranslationEntry) => (
        <Space>
          <HasPermission resource="translations" action="update">
            <Tooltip title="Auto-translate missing">
              <Button
                type="text"
                size="small"
                icon={<TranslationOutlined />}
                onClick={() => {
                  // Auto-translate logic would go here
                  console.log('Auto-translate:', record.key);
                }}
              />
            </Tooltip>
          </HasPermission>
          
          <HasPermission resource="translations" action="delete">
            <Popconfirm
              title="Delete this translation?"
              onConfirm={() => {
                // Delete logic would go here
                console.log('Delete:', record.key);
              }}
            >
              <Button
                type="text"
                size="small"
                danger
                icon={<CloseOutlined />}
              />
            </Popconfirm>
          </HasPermission>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Translation Management</h2>
          <p className="text-gray-500">
            Manage translations across {APP_CONFIG.i18n.supportedLanguages.length} languages
          </p>
        </div>
        
        <Space>
          <HasPermission resource="translations" action="create">
            <Button icon={<UploadOutlined />}>
              Import
            </Button>
          </HasPermission>
          
          <HasPermission resource="translations" action="read">
            <Button icon={<DownloadOutlined />}>
              Export
            </Button>
          </HasPermission>
        </Space>
      </div>

      {/* Filters */}
      <Card size="small">
        <div className="flex items-center space-x-4 flex-wrap">
          <Input.Search
            placeholder="Search translations..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 150 }}
          >
            <Option value="all">All Status</Option>
            <Option value="complete">Complete</Option>
            <Option value="incomplete">Incomplete</Option>
            <Option value="needs_review">Needs Review</Option>
          </Select>
          
          <div className="flex items-center space-x-2">
            <Switch
              checked={showOnlyIncomplete}
              onChange={setShowOnlyIncomplete}
              size="small"
            />
            <span className="text-sm">Show only incomplete</span>
          </div>
        </div>
      </Card>

      {/* Translation Table */}
      <Table
        columns={columns}
        dataSource={filteredTranslations}
        loading={loading}
        rowKey="id"
        scroll={{ x: 1200 }}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} translations`,
        }}
      />
    </div>
  );
}

export default TranslationManager;
