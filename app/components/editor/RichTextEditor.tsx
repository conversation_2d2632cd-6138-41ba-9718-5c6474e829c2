/**
 * Rich Text Editor with Live Preview
 * Advanced WYSIWYG editor with real-time preview and translation support
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, Tabs, Button, Space, Tooltip, Select, Switch, Divider } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  LinkOutlined,
  PictureOutlined,
  TableOutlined,
  CodeOutlined,
  TranslationOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { APP_CONFIG } from '../../config/app';

const { TabPane } = Tabs;
const { Option } = Select;

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  onSave?: (value: string) => void;
  placeholder?: string;
  height?: number;
  readOnly?: boolean;
  showPreview?: boolean;
  showTranslation?: boolean;
  supportedLanguages?: string[];
  maxLength?: number;
  autoSave?: boolean;
  autoSaveInterval?: number;
}

interface EditorCommand {
  command: string;
  value?: string;
}

export function RichTextEditor({
  value = '',
  onChange,
  onSave,
  placeholder = 'Start typing...',
  height = 400,
  readOnly = false,
  showPreview = true,
  showTranslation = false,
  supportedLanguages = ['en', 'es', 'fr', 'de'],
  maxLength = 50000,
  autoSave = false,
  autoSaveInterval = 30000,
}: RichTextEditorProps) {
  const { t, i18n } = useTranslation();
  const [content, setContent] = useState(value);
  const [activeTab, setActiveTab] = useState<'edit' | 'preview' | 'split'>('edit');
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isTranslating, setIsTranslating] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  
  const editorRef = useRef<HTMLDivElement>(null);
  const autoSaveRef = useRef<NodeJS.Timeout>();

  // Update content when value prop changes
  useEffect(() => {
    if (value !== content) {
      setContent(value);
      updateCounts(value);
    }
  }, [value]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onChange) {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
      
      autoSaveRef.current = setTimeout(() => {
        onChange(content);
      }, autoSaveInterval);
    }

    return () => {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
    };
  }, [content, autoSave, autoSaveInterval, onChange]);

  const updateCounts = useCallback((text: string) => {
    const plainText = text.replace(/<[^>]*>/g, '');
    setWordCount(plainText.trim().split(/\s+/).filter(word => word.length > 0).length);
    setCharacterCount(plainText.length);
  }, []);

  const handleContentChange = useCallback((newContent: string) => {
    if (newContent.length <= maxLength) {
      setContent(newContent);
      updateCounts(newContent);
      
      if (onChange && !autoSave) {
        onChange(newContent);
      }
    }
  }, [maxLength, onChange, autoSave, updateCounts]);

  const executeCommand = useCallback((command: EditorCommand) => {
    if (readOnly) return;

    document.execCommand(command.command, false, command.value);
    
    // Update content after command execution
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      handleContentChange(newContent);
    }
  }, [readOnly, handleContentChange]);

  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(content);
    }
  }, [content, onSave]);

  const insertLink = useCallback(() => {
    const url = prompt('Enter URL:');
    if (url) {
      executeCommand({ command: 'createLink', value: url });
    }
  }, [executeCommand]);

  const insertImage = useCallback(() => {
    const url = prompt('Enter image URL:');
    if (url) {
      executeCommand({ command: 'insertImage', value: url });
    }
  }, [executeCommand]);

  const insertTable = useCallback(() => {
    const rows = prompt('Number of rows:', '3');
    const cols = prompt('Number of columns:', '3');
    
    if (rows && cols) {
      const rowCount = parseInt(rows);
      const colCount = parseInt(cols);
      
      let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
      
      for (let i = 0; i < rowCount; i++) {
        tableHTML += '<tr>';
        for (let j = 0; j < colCount; j++) {
          tableHTML += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
        }
        tableHTML += '</tr>';
      }
      
      tableHTML += '</table>';
      
      executeCommand({ command: 'insertHTML', value: tableHTML });
    }
  }, [executeCommand]);

  const translateContent = useCallback(async (targetLanguage: string) => {
    if (!showTranslation || isTranslating) return;

    setIsTranslating(true);
    
    try {
      // Mock translation API call
      // In a real implementation, this would call a translation service
      const translatedContent = await mockTranslate(content, currentLanguage, targetLanguage);
      
      setTranslations(prev => ({
        ...prev,
        [targetLanguage]: translatedContent,
      }));
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  }, [content, currentLanguage, showTranslation, isTranslating]);

  const renderToolbar = () => (
    <div className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700">
      <Space wrap>
        {/* Basic formatting */}
        <Button
          size="small"
          icon={<BoldOutlined />}
          onClick={() => executeCommand({ command: 'bold' })}
          disabled={readOnly}
        />
        <Button
          size="small"
          icon={<ItalicOutlined />}
          onClick={() => executeCommand({ command: 'italic' })}
          disabled={readOnly}
        />
        <Button
          size="small"
          icon={<UnderlineOutlined />}
          onClick={() => executeCommand({ command: 'underline' })}
          disabled={readOnly}
        />
        
        <Divider type="vertical" />
        
        {/* Links and media */}
        <Tooltip title="Insert Link">
          <Button
            size="small"
            icon={<LinkOutlined />}
            onClick={insertLink}
            disabled={readOnly}
          />
        </Tooltip>
        <Tooltip title="Insert Image">
          <Button
            size="small"
            icon={<PictureOutlined />}
            onClick={insertImage}
            disabled={readOnly}
          />
        </Tooltip>
        <Tooltip title="Insert Table">
          <Button
            size="small"
            icon={<TableOutlined />}
            onClick={insertTable}
            disabled={readOnly}
          />
        </Tooltip>
        
        <Divider type="vertical" />
        
        {/* Undo/Redo */}
        <Button
          size="small"
          icon={<UndoOutlined />}
          onClick={() => executeCommand({ command: 'undo' })}
          disabled={readOnly}
        />
        <Button
          size="small"
          icon={<RedoOutlined />}
          onClick={() => executeCommand({ command: 'redo' })}
          disabled={readOnly}
        />
        
        {showTranslation && (
          <>
            <Divider type="vertical" />
            <Select
              size="small"
              value={currentLanguage}
              onChange={setCurrentLanguage}
              style={{ width: 100 }}
            >
              {supportedLanguages.map(lang => (
                <Option key={lang} value={lang}>
                  {lang.toUpperCase()}
                </Option>
              ))}
            </Select>
            <Button
              size="small"
              icon={<TranslationOutlined />}
              loading={isTranslating}
              onClick={() => {
                const targetLang = supportedLanguages.find(lang => lang !== currentLanguage);
                if (targetLang) {
                  translateContent(targetLang);
                }
              }}
              disabled={readOnly}
            >
              Translate
            </Button>
          </>
        )}
      </Space>
      
      <Space>
        {/* Word/Character count */}
        <span className="text-sm text-gray-500">
          {wordCount} words, {characterCount}/{maxLength} characters
        </span>
        
        {onSave && (
          <Button
            type="primary"
            size="small"
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            Save
          </Button>
        )}
      </Space>
    </div>
  );

  const renderEditor = () => (
    <div
      ref={editorRef}
      contentEditable={!readOnly}
      className={`p-4 min-h-[300px] focus:outline-none ${
        readOnly ? 'bg-gray-50 dark:bg-gray-800' : 'bg-white dark:bg-gray-900'
      }`}
      style={{ height: height - 100 }}
      dangerouslySetInnerHTML={{ __html: content }}
      onInput={(e) => {
        const target = e.target as HTMLDivElement;
        handleContentChange(target.innerHTML);
      }}
      onPaste={(e) => {
        e.preventDefault();
        const text = e.clipboardData.getData('text/plain');
        executeCommand({ command: 'insertText', value: text });
      }}
      placeholder={placeholder}
    />
  );

  const renderPreview = () => (
    <div
      className="p-4 prose prose-sm max-w-none dark:prose-invert"
      style={{ height: height - 100, overflow: 'auto' }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );

  const renderSplitView = () => (
    <div className="flex" style={{ height: height - 100 }}>
      <div className="flex-1 border-r border-gray-200 dark:border-gray-700">
        {renderEditor()}
      </div>
      <div className="flex-1">
        {renderPreview()}
      </div>
    </div>
  );

  const renderTranslations = () => (
    <div className="space-y-4">
      {Object.entries(translations).map(([lang, translatedContent]) => (
        <Card key={lang} size="small" title={`Translation (${lang.toUpperCase()})`}>
          <div
            className="prose prose-sm max-w-none dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: translatedContent }}
          />
        </Card>
      ))}
    </div>
  );

  return (
    <Card className="w-full">
      {renderToolbar()}
      
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as any)}
        tabBarStyle={{ margin: 0, padding: '0 16px' }}
      >
        <TabPane tab={<><EditOutlined /> Edit</>} key="edit">
          {renderEditor()}
        </TabPane>
        
        {showPreview && (
          <TabPane tab={<><EyeOutlined /> Preview</>} key="preview">
            {renderPreview()}
          </TabPane>
        )}
        
        {showPreview && (
          <TabPane tab="Split View" key="split">
            {renderSplitView()}
          </TabPane>
        )}
        
        {showTranslation && Object.keys(translations).length > 0 && (
          <TabPane tab={<><TranslationOutlined /> Translations</>} key="translations">
            {renderTranslations()}
          </TabPane>
        )}
      </Tabs>
    </Card>
  );
}

// Mock translation function
async function mockTranslate(
  content: string,
  fromLang: string,
  toLang: string
): Promise<string> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock translation (in real implementation, use Google Translate API, etc.)
  const translations: Record<string, Record<string, string>> = {
    en: {
      es: content.replace(/Hello/g, 'Hola').replace(/World/g, 'Mundo'),
      fr: content.replace(/Hello/g, 'Bonjour').replace(/World/g, 'Monde'),
      de: content.replace(/Hello/g, 'Hallo').replace(/World/g, 'Welt'),
    },
  };
  
  return translations[fromLang]?.[toLang] || content;
}

export default RichTextEditor;
