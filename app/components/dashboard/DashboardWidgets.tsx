/**
 * Dashboard Widgets
 * Comprehensive dashboard with customizable widgets and real-time data
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  List,
  Avatar,
  Badge,
  Button,
  Dropdown,
  Space,
  Typography,
  Alert,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  MoreOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import { useHttp } from '../../hooks/useHttp';
import { HasPermission } from '../../lib/rbac/advanced/PermissionRenderer';

const { Title, Text } = Typography;

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyGrowth: number;
  newSignups: number;
  conversionRate: number;
  systemHealth: number;
  storageUsed: number;
}

interface RecentActivity {
  id: string;
  user: {
    name: string;
    avatar?: string;
  };
  action: string;
  resource: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error';
}

interface SystemAlert {
  id: string;
  type: 'info' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
}

export function DashboardWidgets() {
  const { user } = useSelector((state: RootState) => state.auth);
  const [refreshing, setRefreshing] = useState(false);
  
  // Fetch dashboard data
  const {
    data: stats,
    loading: statsLoading,
    execute: fetchStats,
  } = useHttp<DashboardStats>('/dashboard/stats', 'GET', {
    immediate: true,
  });

  const {
    data: recentActivity,
    loading: activityLoading,
    execute: fetchActivity,
  } = useHttp<RecentActivity[]>('/dashboard/activity', 'GET', {
    immediate: true,
  });

  const {
    data: systemAlerts,
    loading: alertsLoading,
    execute: fetchAlerts,
  } = useHttp<SystemAlert[]>('/dashboard/alerts', 'GET', {
    immediate: true,
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchActivity(),
        fetchAlerts(),
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  // Mock data for demonstration
  const mockStats: DashboardStats = {
    totalUsers: 12543,
    activeUsers: 8932,
    totalRevenue: 245678,
    monthlyGrowth: 12.5,
    newSignups: 234,
    conversionRate: 3.2,
    systemHealth: 98.5,
    storageUsed: 67.8,
  };

  const mockActivity: RecentActivity[] = [
    {
      id: '1',
      user: { name: 'John Doe', avatar: undefined },
      action: 'Created',
      resource: 'User Account',
      timestamp: '2 minutes ago',
      status: 'success',
    },
    {
      id: '2',
      user: { name: 'Jane Smith', avatar: undefined },
      action: 'Updated',
      resource: 'Content Page',
      timestamp: '5 minutes ago',
      status: 'success',
    },
    {
      id: '3',
      user: { name: 'Bob Wilson', avatar: undefined },
      action: 'Failed Login',
      resource: 'Authentication',
      timestamp: '10 minutes ago',
      status: 'error',
    },
  ];

  const mockAlerts: SystemAlert[] = [
    {
      id: '1',
      type: 'warning',
      title: 'High Memory Usage',
      message: 'System memory usage is at 85%',
      timestamp: '1 hour ago',
      resolved: false,
    },
    {
      id: '2',
      type: 'info',
      title: 'Backup Completed',
      message: 'Daily backup completed successfully',
      timestamp: '2 hours ago',
      resolved: true,
    },
  ];

  const currentStats = stats || mockStats;
  const currentActivity = recentActivity || mockActivity;
  const currentAlerts = systemAlerts || mockAlerts;

  const renderStatCard = (
    title: string,
    value: number | string,
    prefix?: React.ReactNode,
    suffix?: string,
    trend?: number,
    color?: string
  ) => (
    <Card>
      <Statistic
        title={title}
        value={value}
        prefix={prefix}
        suffix={suffix}
        valueStyle={{ color }}
      />
      {trend !== undefined && (
        <div className="mt-2">
          <Text type={trend >= 0 ? 'success' : 'danger'}>
            {trend >= 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
            {Math.abs(trend)}%
          </Text>
          <Text type="secondary" className="ml-2">
            vs last month
          </Text>
        </div>
      )}
    </Card>
  );

  const activityColumns = [
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      render: (user: RecentActivity['user']) => (
        <div className="flex items-center space-x-2">
          <Avatar size="small" src={user.avatar} icon={<UserOutlined />} />
          <span>{user.name}</span>
        </div>
      ),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
    },
    {
      title: 'Resource',
      dataIndex: 'resource',
      key: 'resource',
    },
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: RecentActivity['status']) => (
        <Badge
          status={status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'error'}
          text={status.charAt(0).toUpperCase() + status.slice(1)}
        />
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-0">
            Welcome back, {user?.firstName}!
          </Title>
          <Text type="secondary">
            Here's what's happening with your admin panel today.
          </Text>
        </div>
        <Button
          icon={<ReloadOutlined />}
          loading={refreshing}
          onClick={handleRefresh}
        >
          Refresh
        </Button>
      </div>

      {/* System Alerts */}
      {currentAlerts.filter(alert => !alert.resolved).length > 0 && (
        <div className="space-y-2">
          {currentAlerts
            .filter(alert => !alert.resolved)
            .map(alert => (
              <Alert
                key={alert.id}
                type={alert.type}
                message={alert.title}
                description={alert.message}
                showIcon
                closable
                action={
                  <Button size="small" type="text">
                    View Details
                  </Button>
                }
              />
            ))}
        </div>
      )}

      {/* Stats Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <HasPermission resource="users" action="read" fallback={null}>
            {renderStatCard(
              'Total Users',
              currentStats.totalUsers,
              <TeamOutlined />,
              undefined,
              12.5,
              '#1890ff'
            )}
          </HasPermission>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <HasPermission resource="users" action="read" fallback={null}>
            {renderStatCard(
              'Active Users',
              currentStats.activeUsers,
              <UserOutlined />,
              undefined,
              8.2,
              '#52c41a'
            )}
          </HasPermission>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <HasPermission resource="reports" action="read" fallback={null}>
            {renderStatCard(
              'Revenue',
              currentStats.totalRevenue,
              <DollarOutlined />,
              undefined,
              currentStats.monthlyGrowth,
              '#faad14'
            )}
          </HasPermission>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <HasPermission resource="reports" action="read" fallback={null}>
            {renderStatCard(
              'Conversion Rate',
              currentStats.conversionRate,
              <ShoppingCartOutlined />,
              '%',
              -2.1,
              '#f5222d'
            )}
          </HasPermission>
        </Col>
      </Row>

      {/* Charts and Activity */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card
            title="Recent Activity"
            loading={activityLoading}
            extra={
              <HasPermission resource="audit" action="read">
                <Button type="text" size="small">
                  View All
                </Button>
              </HasPermission>
            }
          >
            <Table
              columns={activityColumns}
              dataSource={currentActivity}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <div className="space-y-4">
            {/* System Health */}
            <Card title="System Health" size="small">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <Text>Overall Health</Text>
                    <Text>{currentStats.systemHealth}%</Text>
                  </div>
                  <Progress
                    percent={currentStats.systemHealth}
                    status={currentStats.systemHealth > 95 ? 'success' : 'normal'}
                    size="small"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <Text>Storage Used</Text>
                    <Text>{currentStats.storageUsed}%</Text>
                  </div>
                  <Progress
                    percent={currentStats.storageUsed}
                    status={currentStats.storageUsed > 80 ? 'exception' : 'normal'}
                    size="small"
                  />
                </div>
              </div>
            </Card>

            {/* Quick Actions */}
            <Card title="Quick Actions" size="small">
              <div className="space-y-2">
                <HasPermission resource="users" action="create">
                  <Button block type="text" className="text-left">
                    <UserOutlined /> Add New User
                  </Button>
                </HasPermission>
                
                <HasPermission resource="content" action="create">
                  <Button block type="text" className="text-left">
                    <EditOutlined /> Create Content
                  </Button>
                </HasPermission>
                
                <HasPermission resource="reports" action="read">
                  <Button block type="text" className="text-left">
                    <EyeOutlined /> View Reports
                  </Button>
                </HasPermission>
                
                <HasPermission resource="settings" action="read">
                  <Button block type="text" className="text-left">
                    <SettingOutlined /> System Settings
                  </Button>
                </HasPermission>
              </div>
            </Card>
          </div>
        </Col>
      </Row>
    </div>
  );
}

export default DashboardWidgets;
