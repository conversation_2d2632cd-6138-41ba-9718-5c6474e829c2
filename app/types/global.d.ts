/**
 * Global TypeScript Declarations
 * Available throughout the application without imports
 */

declare global {
  // Environment Variables
  interface ImportMetaEnv {
    readonly VITE_API_BASE_URL: string;
    readonly VITE_APP_NAME: string;
    readonly VITE_APP_VERSION: string;
    readonly VITE_ENCRYPTION_KEY: string;
    readonly VITE_SESSION_TIMEOUT: string;
    readonly VITE_MAX_LOGIN_ATTEMPTS: string;
    readonly VITE_RATE_LIMIT_WINDOW: string;
    readonly VITE_RATE_LIMIT_MAX: string;
    readonly VITE_ENABLE_AUDIT_LOGS: string;
    readonly VITE_MONGODB_URI: string;
    readonly VITE_JWT_SECRET: string;
    readonly VITE_REFRESH_TOKEN_SECRET: string;
    readonly VITE_CSRF_SECRET: string;
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv;
  }

  // User & Authentication Types
  interface User {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    role: UserRole;
    permissions: Permission[];
    isActive: boolean;
    emailVerified: boolean;
    lastLoginAt?: string;
    createdAt: string;
    updatedAt: string;
    preferences: UserPreferences;
    twoFactorEnabled: boolean;
  }

  interface UserPreferences {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    notifications: NotificationSettings;
    dashboard: DashboardSettings;
  }

  interface NotificationSettings {
    email: boolean;
    push: boolean;
    security: boolean;
    updates: boolean;
  }

  interface DashboardSettings {
    layout: 'grid' | 'list';
    widgets: string[];
    refreshInterval: number;
  }

  type UserRole = 'super_admin' | 'admin' | 'moderator' | 'editor' | 'user' | 'viewer';

  interface Permission {
    id: string;
    name: string;
    resource: string;
    action: PermissionAction;
    conditions?: PermissionCondition[];
  }

  type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'manage' | 'execute';

  interface PermissionCondition {
    field: string;
    operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte';
    value: any;
  }

  // Authentication Types
  interface AuthState {
    user: User | null;
    token: string | null;
    refreshToken: string | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
    sessionTimeout: number | null;
    rememberMe: boolean;
    loginAttempts: number;
    isLocked: boolean;
    lockoutUntil: number | null;
  }

  interface LoginCredentials {
    email: string;
    password: string;
    rememberMe?: boolean;
    captcha?: string;
  }

  interface RegisterData {
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
    username?: string;
    acceptTerms: boolean;
  }

  interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
  }

  // API Types
  interface ApiResponse<T = any> {
    success: boolean;
    data: T;
    message?: string;
    errors?: Record<string, string[]>;
    meta?: {
      pagination?: PaginationMeta;
      total?: number;
      page?: number;
      limit?: number;
    };
  }

  interface ApiError {
    message: string;
    code: string;
    status: number;
    details?: Record<string, any>;
    timestamp: string;
  }

  interface PaginationMeta {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }

  interface SearchParams {
    query?: string;
    filters?: Record<string, any>;
    sort?: {
      field: string;
      order: 'asc' | 'desc';
    };
    page?: number;
    limit?: number;
  }

  // HTTP Hook Types
  interface UseHttpOptions {
    immediate?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: ApiError) => void;
    retries?: number;
    retryDelay?: number;
    timeout?: number;
  }

  interface UseHttpReturn<T = any> {
    data: T | null;
    loading: boolean;
    error: ApiError | null;
    execute: (params?: any) => Promise<T>;
    reset: () => void;
    cancel: () => void;
  }

  // UI Component Types
  interface TableColumn<T = any> {
    key: string;
    title: string;
    dataIndex?: string;
    render?: (value: any, record: T, index: number) => React.ReactNode;
    sorter?: boolean | ((a: T, b: T) => number);
    filters?: { text: string; value: any }[];
    width?: number | string;
    fixed?: 'left' | 'right';
    ellipsis?: boolean;
    align?: 'left' | 'center' | 'right';
  }

  interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'date' | 'file';
    required?: boolean;
    placeholder?: string;
    options?: { label: string; value: any }[];
    validation?: any;
    disabled?: boolean;
    hidden?: boolean;
  }

  interface ModalProps {
    open: boolean;
    title: string;
    onOk?: () => void;
    onCancel?: () => void;
    loading?: boolean;
    width?: number;
    centered?: boolean;
    maskClosable?: boolean;
  }

  // Security Types
  interface SecurityEvent {
    id: string;
    type: SecurityEventType;
    userId?: string;
    ip: string;
    userAgent: string;
    details: Record<string, any>;
    timestamp: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }

  type SecurityEventType = 
    | 'login_success'
    | 'login_failed'
    | 'logout'
    | 'password_change'
    | 'account_locked'
    | 'suspicious_activity'
    | 'permission_denied'
    | 'data_access'
    | 'data_modification'
    | 'admin_action';

  interface AuditLog {
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    ip: string;
    userAgent: string;
    timestamp: string;
    success: boolean;
    error?: string;
  }

  // Route Types
  interface RouteConfig {
    path: string;
    component: React.ComponentType;
    exact?: boolean;
    roles?: UserRole[];
    permissions?: string[];
    public?: boolean;
    redirect?: string;
    layout?: 'admin' | 'auth' | 'public';
  }

  interface BreadcrumbItem {
    title: string;
    path?: string;
    icon?: React.ReactNode;
  }

  // Theme Types
  interface ThemeConfig {
    mode: 'light' | 'dark';
    primaryColor: string;
    borderRadius: number;
    fontSize: number;
    fontFamily: string;
    compactMode: boolean;
  }

  // Translation Types
  interface Translation {
    id: string;
    key: string;
    language: string;
    value: string;
    namespace: string;
    createdAt: string;
    updatedAt: string;
  }

  interface Language {
    code: string;
    name: string;
    nativeName: string;
    flag: string;
    rtl: boolean;
    enabled: boolean;
  }

  // Dashboard Types
  interface DashboardWidget {
    id: string;
    type: 'chart' | 'stat' | 'table' | 'list' | 'custom';
    title: string;
    data: any;
    config: Record<string, any>;
    position: { x: number; y: number; w: number; h: number };
    permissions?: string[];
  }

  interface DashboardStats {
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    growthRate: number;
    newSignups: number;
    conversionRate: number;
  }

  // File Upload Types
  interface FileUpload {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
    thumbnailUrl?: string;
    uploadedBy: string;
    uploadedAt: string;
  }

  // Notification Types
  interface Notification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    read: boolean;
    createdAt: string;
    actions?: NotificationAction[];
  }

  interface NotificationAction {
    label: string;
    action: () => void;
    type?: 'primary' | 'default';
  }

  // Settings Types
  interface SystemSettings {
    siteName: string;
    siteDescription: string;
    logo: string;
    favicon: string;
    timezone: string;
    dateFormat: string;
    currency: string;
    language: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
    emailVerificationRequired: boolean;
    twoFactorRequired: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordPolicy: PasswordPolicy;
  }

  interface PasswordPolicy {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    preventReuse: number;
    expirationDays: number;
  }

  // Utility Types
  type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
  };

  type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

  type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

  // Window Extensions
  interface Window {
    __REDUX_DEVTOOLS_EXTENSION_COMPOSE__?: any;
    __APP_CONFIG__?: Record<string, any>;
  }
}

export {};
