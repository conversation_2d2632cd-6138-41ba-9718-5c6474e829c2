/**
 * HTTP Client Service
 * Axios-based HTTP client with security features, interceptors, and error handling
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { secureStorage } from '../utils/secureStorage';
import { SecurityUtils, SECURITY_CONFIG } from '../config/security';
import { APP_CONFIG } from '../config/app';

class HttpClient {
  private client: AxiosInstance;
  private refreshPromise: Promise<string> | null = null;
  private requestQueue: Array<{
    resolve: (token: string) => void;
    reject: (error: any) => void;
  }> = [];

  constructor() {
    this.client = axios.create({
      baseURL: APP_CONFIG.api.baseURL,
      timeout: SECURITY_CONFIG.API.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => this.handleRequest(config),
      (error) => this.handleRequestError(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => this.handleResponse(response),
      (error) => this.handleResponseError(error)
    );
  }

  private handleRequest(config: AxiosRequestConfig): AxiosRequestConfig {
    // Add authentication token
    const tokens = secureStorage.getAuthTokens();
    if (tokens?.accessToken) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${tokens.accessToken}`,
      };
    }

    // Add CSRF token
    const csrfToken = secureStorage.getCSRFToken();
    if (csrfToken) {
      config.headers = {
        ...config.headers,
        'X-CSRF-Token': csrfToken,
      };
    }

    // Add request ID for tracking
    const requestId = SecurityUtils.generateSecureToken(16);
    config.headers = {
      ...config.headers,
      'X-Request-ID': requestId,
    };

    // Add timestamp
    config.headers = {
      ...config.headers,
      'X-Timestamp': Date.now().toString(),
    };

    // Sanitize request data
    if (config.data && typeof config.data === 'object') {
      config.data = this.sanitizeRequestData(config.data);
    }

    // Log request in development
    if (import.meta.env.NODE_ENV === 'development') {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
      });
    }

    return config;
  }

  private handleRequestError(error: AxiosError): Promise<AxiosError> {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }

  private handleResponse(response: AxiosResponse): AxiosResponse {
    // Log response in development
    if (import.meta.env.NODE_ENV === 'development') {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    // Validate response structure
    if (response.data && typeof response.data === 'object') {
      if (!response.data.hasOwnProperty('success')) {
        console.warn('Response missing success field:', response.data);
      }
    }

    return response;
  }

  private async handleResponseError(error: AxiosError): Promise<any> {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Log error in development
    if (import.meta.env.NODE_ENV === 'development') {
      console.error(`❌ ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        message: error.message,
        data: error.response?.data,
      });
    }

    // Handle token refresh for 401 errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const newToken = await this.refreshAccessToken();
        
        // Update the original request with new token
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
        }

        // Retry the original request
        return this.client(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        this.handleAuthenticationFailure();
        return Promise.reject(refreshError);
      }
    }

    // Handle rate limiting
    if (error.response?.status === 429) {
      const retryAfter = error.response.headers['retry-after'];
      const delay = retryAfter ? parseInt(retryAfter) * 1000 : 1000;
      
      console.warn(`Rate limited. Retrying after ${delay}ms`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.client(originalRequest);
    }

    // Handle network errors
    if (!error.response) {
      const networkError: ApiError = {
        message: 'Network error. Please check your connection.',
        code: 'NETWORK_ERROR',
        status: 0,
        details: { originalError: error.message },
        timestamp: new Date().toISOString(),
      };
      return Promise.reject(networkError);
    }

    // Transform error to standard format
    const apiError: ApiError = {
      message: error.response.data?.message || error.message || 'Request failed',
      code: error.response.data?.code || 'HTTP_ERROR',
      status: error.response.status,
      details: error.response.data?.details || {},
      timestamp: new Date().toISOString(),
    };

    return Promise.reject(apiError);
  }

  private async refreshAccessToken(): Promise<string> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();

    try {
      const token = await this.refreshPromise;
      this.processRequestQueue(token);
      return token;
    } catch (error) {
      this.processRequestQueue(null, error);
      throw error;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<string> {
    const tokens = secureStorage.getAuthTokens();
    
    if (!tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(
        `${APP_CONFIG.api.baseURL}/auth/refresh`,
        {
          refreshToken: tokens.refreshToken,
        },
        {
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Token refresh failed');
      }

      const newTokens = response.data.data;
      secureStorage.setAuthTokens(newTokens);

      return newTokens.accessToken;
    } catch (error) {
      secureStorage.clearAuthTokens();
      throw error;
    }
  }

  private processRequestQueue(token: string | null, error?: any) {
    this.requestQueue.forEach(({ resolve, reject }) => {
      if (token) {
        resolve(token);
      } else {
        reject(error || new Error('Token refresh failed'));
      }
    });
    
    this.requestQueue = [];
  }

  private handleAuthenticationFailure() {
    // Clear stored tokens
    secureStorage.clearAuthTokens();
    secureStorage.clearCSRFToken();

    // Redirect to login page
    const loginPath = APP_CONFIG.routes.login;
    if (window.location.pathname !== loginPath) {
      window.location.href = loginPath;
    }
  }

  private sanitizeRequestData(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeRequestData(item));
    }

    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = SecurityUtils.sanitizeInput(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeRequestData(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  // Public methods
  async request<T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.request<T>(config);
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // Utility methods
  setBaseURL(baseURL: string) {
    this.client.defaults.baseURL = baseURL;
  }

  setTimeout(timeout: number) {
    this.client.defaults.timeout = timeout;
  }

  setDefaultHeaders(headers: Record<string, string>) {
    Object.assign(this.client.defaults.headers, headers);
  }

  // Cancel all pending requests
  cancelAllRequests() {
    // This would require tracking all requests with cancel tokens
    // For now, we'll just clear the request queue
    this.requestQueue = [];
  }

  // Get request statistics
  getStats() {
    return {
      baseURL: this.client.defaults.baseURL,
      timeout: this.client.defaults.timeout,
      queueLength: this.requestQueue.length,
      isRefreshing: !!this.refreshPromise,
    };
  }
}

// Export singleton instance
export const httpClient = new HttpClient();
export default httpClient;
