/**
 * Authentication Service
 * Handles all authentication-related operations with security features
 */

import axios, { AxiosResponse } from 'axios';
import { secureStorage } from '../utils/secureStorage';
import { SECURITY_CONFIG, SecurityUtils } from '../config/security';
import { APP_CONFIG } from '../config/app';

class AuthService {
  private baseURL: string;
  private sessionTimeoutId: NodeJS.Timeout | null = null;
  private tokenRefreshPromise: Promise<AuthTokens> | null = null;

  constructor() {
    this.baseURL = APP_CONFIG.api.baseURL;
    this.setupSessionTimeout();
  }

  /**
   * Login with credentials
   */
  async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Check if account is locked
      if (secureStorage.isAccountLocked()) {
        const lockoutUntil = secureStorage.getLockoutUntil();
        const remainingTime = lockoutUntil ? Math.ceil((lockoutUntil - Date.now()) / 1000 / 60) : 0;
        throw new Error(`Account is locked. Try again in ${remainingTime} minutes.`);
      }

      // Validate password strength if it's a new login
      const passwordValidation = SecurityUtils.validatePassword(credentials.password);
      if (!passwordValidation.isValid) {
        throw new Error('Password does not meet security requirements');
      }

      // Add CSRF token
      const csrfToken = SecurityUtils.generateCSRFToken();
      secureStorage.setCSRFToken(csrfToken);

      const response: AxiosResponse<ApiResponse<{ user: User; tokens: AuthTokens }>> = await axios.post(
        `${this.baseURL}/auth/login`,
        {
          ...credentials,
          csrfToken,
          deviceInfo: this.getDeviceInfo(),
        },
        {
          headers: {
            'X-CSRF-Token': csrfToken,
            'Content-Type': 'application/json',
          },
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        this.handleFailedLogin();
        throw new Error(response.data.message || 'Login failed');
      }

      const { user, tokens } = response.data.data;

      // Store tokens securely
      secureStorage.setAuthTokens(tokens);
      secureStorage.setRememberMe(credentials.rememberMe || false);
      secureStorage.clearLoginAttempts();
      secureStorage.clearLockout();

      // Setup session timeout
      this.setupSessionTimeout();

      // Log successful login
      this.logSecurityEvent('login_success', { userId: user.id });

      return { user, tokens };
    } catch (error) {
      this.handleFailedLogin();
      throw this.handleAuthError(error);
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Validate password
      const passwordValidation = SecurityUtils.validatePassword(userData.password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // Check password confirmation
      if (userData.password !== userData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const csrfToken = SecurityUtils.generateCSRFToken();
      secureStorage.setCSRFToken(csrfToken);

      const response: AxiosResponse<ApiResponse<{ user: User; tokens: AuthTokens }>> = await axios.post(
        `${this.baseURL}/auth/register`,
        {
          ...userData,
          csrfToken,
          deviceInfo: this.getDeviceInfo(),
        },
        {
          headers: {
            'X-CSRF-Token': csrfToken,
            'Content-Type': 'application/json',
          },
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Registration failed');
      }

      const { user, tokens } = response.data.data;

      // Store tokens securely
      secureStorage.setAuthTokens(tokens);
      this.setupSessionTimeout();

      // Log successful registration
      this.logSecurityEvent('registration_success', { userId: user.id });

      return { user, tokens };
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<AuthTokens> {
    // Prevent multiple simultaneous refresh requests
    if (this.tokenRefreshPromise) {
      return this.tokenRefreshPromise;
    }

    this.tokenRefreshPromise = this.performTokenRefresh();
    
    try {
      const tokens = await this.tokenRefreshPromise;
      return tokens;
    } finally {
      this.tokenRefreshPromise = null;
    }
  }

  /**
   * Perform actual token refresh
   */
  private async performTokenRefresh(): Promise<AuthTokens> {
    try {
      const currentTokens = secureStorage.getAuthTokens();
      if (!currentTokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      const response: AxiosResponse<ApiResponse<AuthTokens>> = await axios.post(
        `${this.baseURL}/auth/refresh`,
        {
          refreshToken: currentTokens.refreshToken,
          deviceInfo: this.getDeviceInfo(),
        },
        {
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Token refresh failed');
      }

      const tokens = response.data.data;
      secureStorage.setAuthTokens(tokens);
      this.setupSessionTimeout();

      return tokens;
    } catch (error) {
      // If refresh fails, logout user
      this.logout();
      throw this.handleAuthError(error);
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const tokens = secureStorage.getAuthTokens();
      
      if (tokens?.accessToken) {
        // Notify server about logout
        await axios.post(
          `${this.baseURL}/auth/logout`,
          { refreshToken: tokens.refreshToken },
          {
            headers: {
              Authorization: `Bearer ${tokens.accessToken}`,
            },
            timeout: 5000, // Shorter timeout for logout
          }
        ).catch(() => {
          // Ignore logout API errors
        });
      }

      // Clear all stored data
      this.clearAuthData();

      // Log logout
      this.logSecurityEvent('logout');
    } catch (error) {
      // Always clear data even if API call fails
      this.clearAuthData();
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      // Validate new password
      const passwordValidation = SecurityUtils.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      const tokens = secureStorage.getAuthTokens();
      if (!tokens?.accessToken) {
        throw new Error('Not authenticated');
      }

      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${this.baseURL}/auth/change-password`,
        {
          currentPassword,
          newPassword,
          csrfToken: secureStorage.getCSRFToken(),
        },
        {
          headers: {
            Authorization: `Bearer ${tokens.accessToken}`,
            'X-CSRF-Token': secureStorage.getCSRFToken(),
          },
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Password change failed');
      }

      // Log password change
      this.logSecurityEvent('password_change');
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${this.baseURL}/auth/forgot-password`,
        { email },
        {
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Password reset request failed');
      }
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      // Validate new password
      const passwordValidation = SecurityUtils.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${this.baseURL}/auth/reset-password`,
        {
          token,
          newPassword,
        },
        {
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Password reset failed');
      }

      // Log password reset
      this.logSecurityEvent('password_reset');
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<User> {
    try {
      const tokens = secureStorage.getAuthTokens();
      if (!tokens?.accessToken) {
        throw new Error('Not authenticated');
      }

      const response: AxiosResponse<ApiResponse<User>> = await axios.get(
        `${this.baseURL}/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${tokens.accessToken}`,
          },
          timeout: SECURITY_CONFIG.API.timeout,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to get user data');
      }

      return response.data.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const tokens = secureStorage.getAuthTokens();
    return !!(tokens?.accessToken && !this.isTokenExpired(tokens.accessToken));
  }

  /**
   * Check if token is expired
   */
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  /**
   * Setup session timeout
   */
  private setupSessionTimeout(): void {
    if (this.sessionTimeoutId) {
      clearTimeout(this.sessionTimeoutId);
    }

    const rememberMe = secureStorage.getRememberMe();
    if (!rememberMe) {
      const timeout = SECURITY_CONFIG.SESSION_TIMEOUT;
      const timeoutTimestamp = Date.now() + timeout;
      
      secureStorage.setSessionTimeout(timeoutTimestamp);
      
      this.sessionTimeoutId = setTimeout(() => {
        this.logout();
        this.logSecurityEvent('session_timeout');
      }, timeout);
    }
  }

  /**
   * Handle failed login attempt
   */
  private handleFailedLogin(): void {
    const attempts = secureStorage.getLoginAttempts() + 1;
    secureStorage.setLoginAttempts(attempts);

    if (attempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
      const lockoutUntil = Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION;
      secureStorage.setLockoutUntil(lockoutUntil);
      this.logSecurityEvent('account_locked', { attempts });
    }

    this.logSecurityEvent('login_failed', { attempts });
  }

  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    secureStorage.clearAuthTokens();
    secureStorage.clearCSRFToken();
    
    if (this.sessionTimeoutId) {
      clearTimeout(this.sessionTimeoutId);
      this.sessionTimeoutId = null;
    }
  }

  /**
   * Get device information for security logging
   */
  private getDeviceInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen: {
        width: screen.width,
        height: screen.height,
      },
    };
  }

  /**
   * Log security events
   */
  private logSecurityEvent(type: string, details: Record<string, any> = {}): void {
    if (SECURITY_CONFIG.AUDIT.enabled) {
      // In a real app, this would send to your logging service
      console.log('Security Event:', {
        type,
        details,
        timestamp: new Date().toISOString(),
        ip: 'client-side', // Would be filled by server
        userAgent: navigator.userAgent,
      });
    }
  }

  /**
   * Handle authentication errors
   */
  private handleAuthError(error: any): Error {
    if (axios.isAxiosError(error)) {
      const message = error.response?.data?.message || error.message;
      const status = error.response?.status || 500;
      
      // Log security event for certain errors
      if (status === 401 || status === 403) {
        this.logSecurityEvent('auth_error', { status, message });
      }
      
      return new Error(message);
    }
    
    return error instanceof Error ? error : new Error('Authentication failed');
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
