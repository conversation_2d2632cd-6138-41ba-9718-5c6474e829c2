/**
 * User API Service
 * Handles all user-related API operations
 */

import { httpClient } from '../httpClient';
import { APP_CONFIG } from '../../config/app';

class UserService {
  private baseUrl = APP_CONFIG.routes.api.users;

  /**
   * Get paginated list of users
   */
  async getUsers(params: SearchParams = {}): Promise<ApiResponse<User[]>> {
    const response = await httpClient.post<ApiResponse<User[]>>(
      `${this.baseUrl}/search`,
      params
    );
    return response.data;
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<ApiResponse<User>> {
    const response = await httpClient.get<ApiResponse<User>>(
      `${this.baseUrl}/${id}`
    );
    return response.data;
  }

  /**
   * Create new user
   */
  async createUser(userData: Partial<User>): Promise<ApiResponse<User>> {
    const response = await httpClient.post<ApiResponse<User>>(
      this.baseUrl,
      userData
    );
    return response.data;
  }

  /**
   * Update user
   */
  async updateUser(id: string, userData: Partial<User>): Promise<ApiResponse<User>> {
    const response = await httpClient.put<ApiResponse<User>>(
      `${this.baseUrl}/${id}`,
      userData
    );
    return response.data;
  }

  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<ApiResponse<void>> {
    const response = await httpClient.delete<ApiResponse<void>>(
      `${this.baseUrl}/${id}`
    );
    return response.data;
  }

  /**
   * Bulk delete users
   */
  async bulkDeleteUsers(ids: string[]): Promise<ApiResponse<void>> {
    const response = await httpClient.post<ApiResponse<void>>(
      `${this.baseUrl}/bulk-delete`,
      { ids }
    );
    return response.data;
  }

  /**
   * Update user status
   */
  async updateUserStatus(id: string, isActive: boolean): Promise<ApiResponse<User>> {
    const response = await httpClient.patch<ApiResponse<User>>(
      `${this.baseUrl}/${id}/status`,
      { isActive }
    );
    return response.data;
  }

  /**
   * Reset user password
   */
  async resetUserPassword(id: string): Promise<ApiResponse<{ temporaryPassword: string }>> {
    const response = await httpClient.post<ApiResponse<{ temporaryPassword: string }>>(
      `${this.baseUrl}/${id}/reset-password`
    );
    return response.data;
  }

  /**
   * Update user role
   */
  async updateUserRole(id: string, role: UserRole): Promise<ApiResponse<User>> {
    const response = await httpClient.patch<ApiResponse<User>>(
      `${this.baseUrl}/${id}/role`,
      { role }
    );
    return response.data;
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(id: string): Promise<ApiResponse<Permission[]>> {
    const response = await httpClient.get<ApiResponse<Permission[]>>(
      `${this.baseUrl}/${id}/permissions`
    );
    return response.data;
  }

  /**
   * Update user permissions
   */
  async updateUserPermissions(id: string, permissions: string[]): Promise<ApiResponse<Permission[]>> {
    const response = await httpClient.put<ApiResponse<Permission[]>>(
      `${this.baseUrl}/${id}/permissions`,
      { permissions }
    );
    return response.data;
  }

  /**
   * Get user activity log
   */
  async getUserActivity(id: string, params: SearchParams = {}): Promise<ApiResponse<AuditLog[]>> {
    const response = await httpClient.post<ApiResponse<AuditLog[]>>(
      `${this.baseUrl}/${id}/activity`,
      params
    );
    return response.data;
  }

  /**
   * Export users
   */
  async exportUsers(format: 'csv' | 'excel' | 'pdf', filters?: any): Promise<Blob> {
    const response = await httpClient.post(
      `${this.baseUrl}/export`,
      { format, filters },
      {
        responseType: 'blob',
      }
    );
    return response.data;
  }

  /**
   * Import users from file
   */
  async importUsers(file: File): Promise<ApiResponse<{ imported: number; errors: any[] }>> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await httpClient.post<ApiResponse<{ imported: number; errors: any[] }>>(
      `${this.baseUrl}/import`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<ApiResponse<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRole, number>;
    recentSignups: number;
  }>> {
    const response = await httpClient.get<ApiResponse<any>>(
      `${this.baseUrl}/stats`
    );
    return response.data;
  }

  /**
   * Search users by email or name
   */
  async searchUsers(query: string, limit = 10): Promise<ApiResponse<User[]>> {
    const response = await httpClient.get<ApiResponse<User[]>>(
      `${this.baseUrl}/search`,
      {
        params: { q: query, limit },
      }
    );
    return response.data;
  }

  /**
   * Check if email is available
   */
  async checkEmailAvailability(email: string, excludeUserId?: string): Promise<ApiResponse<{ available: boolean }>> {
    const response = await httpClient.post<ApiResponse<{ available: boolean }>>(
      `${this.baseUrl}/check-email`,
      { email, excludeUserId }
    );
    return response.data;
  }

  /**
   * Send verification email
   */
  async sendVerificationEmail(id: string): Promise<ApiResponse<void>> {
    const response = await httpClient.post<ApiResponse<void>>(
      `${this.baseUrl}/${id}/send-verification`
    );
    return response.data;
  }

  /**
   * Verify user email
   */
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    const response = await httpClient.post<ApiResponse<void>>(
      `${this.baseUrl}/verify-email`,
      { token }
    );
    return response.data;
  }

  /**
   * Enable/disable two-factor authentication
   */
  async updateTwoFactor(id: string, enabled: boolean): Promise<ApiResponse<{ qrCode?: string; backupCodes?: string[] }>> {
    const response = await httpClient.patch<ApiResponse<any>>(
      `${this.baseUrl}/${id}/two-factor`,
      { enabled }
    );
    return response.data;
  }

  /**
   * Get user sessions
   */
  async getUserSessions(id: string): Promise<ApiResponse<{
    id: string;
    ip: string;
    userAgent: string;
    location: string;
    lastActivity: string;
    current: boolean;
  }[]>> {
    const response = await httpClient.get<ApiResponse<any>>(
      `${this.baseUrl}/${id}/sessions`
    );
    return response.data;
  }

  /**
   * Revoke user session
   */
  async revokeUserSession(userId: string, sessionId: string): Promise<ApiResponse<void>> {
    const response = await httpClient.delete<ApiResponse<void>>(
      `${this.baseUrl}/${userId}/sessions/${sessionId}`
    );
    return response.data;
  }

  /**
   * Revoke all user sessions
   */
  async revokeAllUserSessions(id: string): Promise<ApiResponse<void>> {
    const response = await httpClient.delete<ApiResponse<void>>(
      `${this.baseUrl}/${id}/sessions`
    );
    return response.data;
  }
}

export const userService = new UserService();
export default userService;
