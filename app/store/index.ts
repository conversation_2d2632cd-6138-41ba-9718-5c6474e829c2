/**
 * Redux Store Configuration
 * Main store setup with all slices and middleware
 */

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { encryptTransform } from 'redux-persist-transform-encrypt';

// Import slices
import authSlice from './slices/authSlice';
import userSlice from './slices/userSlice';
import themeSlice from './slices/themeSlice';
import settingsSlice from './slices/settingsSlice';
import notificationSlice from './slices/notificationSlice';
import auditSlice from './slices/auditSlice';

// Import API
import { apiSlice } from './api/apiSlice';

// Import middleware
import { authMiddleware } from './middleware/authMiddleware';
import { securityMiddleware } from './middleware/securityMiddleware';
import { loggingMiddleware } from './middleware/loggingMiddleware';

// Encryption transform for sensitive data
const encryptTransform = encryptTransform({
  secretKey: import.meta.env.VITE_ENCRYPTION_KEY || 'default-secret-key',
  onError: (error) => {
    console.error('Redux persist encryption error:', error);
  },
});

// Persist configuration
const persistConfig = {
  key: 'admin_panel_root',
  version: 1,
  storage,
  whitelist: ['auth', 'theme', 'settings'], // Only persist these slices
  blacklist: ['api'], // Don't persist API cache
  transforms: [encryptTransform],
};

// Auth persist configuration (more restrictive)
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'rememberMe'], // Only persist user data and remember me
  blacklist: ['token', 'refreshToken', 'isLoading', 'error'], // Don't persist tokens
  transforms: [encryptTransform],
};

// Root reducer
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authSlice),
  users: userSlice,
  theme: themeSlice,
  settings: settingsSlice,
  notifications: notificationSlice,
  audit: auditSlice,
  api: apiSlice.reducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
          'persist/FLUSH',
        ],
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['items.dates'],
      },
      immutableCheck: {
        warnAfter: 128,
      },
    })
      .concat(apiSlice.middleware)
      .concat(authMiddleware)
      .concat(securityMiddleware)
      .concat(loggingMiddleware),
  devTools: import.meta.env.NODE_ENV !== 'production' && {
    name: 'Admin Panel Store',
    trace: true,
    traceLimit: 25,
  },
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export store actions
export const storeActions = {
  // Auth actions
  login: authSlice.actions.loginStart,
  logout: authSlice.actions.logout,
  setUser: authSlice.actions.setUser,
  clearError: authSlice.actions.clearError,
  
  // Theme actions
  setTheme: themeSlice.actions.setTheme,
  toggleTheme: themeSlice.actions.toggleTheme,
  setCompactMode: themeSlice.actions.setCompactMode,
  
  // Settings actions
  updateSettings: settingsSlice.actions.updateSettings,
  resetSettings: settingsSlice.actions.resetSettings,
  
  // Notification actions
  addNotification: notificationSlice.actions.addNotification,
  removeNotification: notificationSlice.actions.removeNotification,
  markAsRead: notificationSlice.actions.markAsRead,
  clearAll: notificationSlice.actions.clearAll,
  
  // User actions
  setUsers: userSlice.actions.setUsers,
  addUser: userSlice.actions.addUser,
  updateUser: userSlice.actions.updateUser,
  deleteUser: userSlice.actions.deleteUser,
  setLoading: userSlice.actions.setLoading,
  setError: userSlice.actions.setError,
  
  // Audit actions
  addAuditLog: auditSlice.actions.addAuditLog,
  setAuditLogs: auditSlice.actions.setAuditLogs,
  clearAuditLogs: auditSlice.actions.clearAuditLogs,
};

// Store utilities
export const storeUtils = {
  /**
   * Get current auth state
   */
  getAuthState: (): AuthState => store.getState().auth,
  
  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const auth = store.getState().auth;
    return auth.isAuthenticated && !!auth.user;
  },
  
  /**
   * Get current user
   */
  getCurrentUser: (): User | null => store.getState().auth.user,
  
  /**
   * Get user permissions
   */
  getUserPermissions: (): Permission[] => {
    const user = store.getState().auth.user;
    return user?.permissions || [];
  },
  
  /**
   * Check if user has permission
   */
  hasPermission: (resource: string, action: PermissionAction): boolean => {
    const permissions = storeUtils.getUserPermissions();
    return permissions.some(
      (p) => p.resource === resource && p.action === action
    );
  },
  
  /**
   * Check if user has role
   */
  hasRole: (role: UserRole): boolean => {
    const user = store.getState().auth.user;
    return user?.role === role;
  },
  
  /**
   * Get theme configuration
   */
  getTheme: (): ThemeConfig => store.getState().theme,
  
  /**
   * Get system settings
   */
  getSettings: (): SystemSettings => store.getState().settings,
  
  /**
   * Get unread notifications count
   */
  getUnreadNotificationsCount: (): number => {
    const notifications = store.getState().notifications.items;
    return notifications.filter((n) => !n.read).length;
  },
  
  /**
   * Dispatch action with error handling
   */
  safeDispatch: (action: any) => {
    try {
      return store.dispatch(action);
    } catch (error) {
      console.error('Store dispatch error:', error);
      store.dispatch(
        storeActions.addNotification({
          id: Date.now().toString(),
          type: 'error',
          title: 'System Error',
          message: 'An error occurred while processing your request',
          read: false,
          createdAt: new Date().toISOString(),
        })
      );
    }
  },
  
  /**
   * Reset store to initial state
   */
  resetStore: () => {
    persistor.purge();
    store.dispatch({ type: 'RESET_STORE' });
  },
  
  /**
   * Export store state (for debugging)
   */
  exportState: () => {
    const state = store.getState();
    // Remove sensitive data
    const sanitizedState = {
      ...state,
      auth: {
        ...state.auth,
        token: '[REDACTED]',
        refreshToken: '[REDACTED]',
      },
    };
    return JSON.stringify(sanitizedState, null, 2);
  },
};

// Hot module replacement for reducers in development
if (import.meta.env.NODE_ENV === 'development' && import.meta.hot) {
  import.meta.hot.accept('./slices/authSlice', () => {
    store.replaceReducer(persistedReducer);
  });
}

export default store;
