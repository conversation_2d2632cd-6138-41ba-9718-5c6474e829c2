/**
 * Logging Middleware
 * Comprehensive logging for debugging and audit purposes
 */

import { Middleware } from '@reduxjs/toolkit';
import { addAuditLog } from '../slices/auditSlice';
import { SECURITY_CONFIG } from '../../config/security';

interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  action: string;
  payload?: any;
  state?: any;
  duration?: number;
  error?: any;
  userId?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private sensitiveFields = SECURITY_CONFIG.AUDIT.sensitiveFields;

  log(entry: LogEntry) {
    // Sanitize sensitive data
    const sanitizedEntry = this.sanitizeLogEntry(entry);
    
    this.logs.unshift(sanitizedEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }
    
    // Console output in development
    if (import.meta.env.NODE_ENV === 'development') {
      const color = this.getLogColor(entry.level);
      console.log(
        `%c[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.action}`,
        `color: ${color}`,
        sanitizedEntry
      );
    }
  }

  private sanitizeLogEntry(entry: LogEntry): LogEntry {
    const sanitized = { ...entry };
    
    // Remove sensitive fields from payload
    if (sanitized.payload) {
      sanitized.payload = this.sanitizeObject(sanitized.payload);
    }
    
    // Remove sensitive fields from state
    if (sanitized.state) {
      sanitized.state = this.sanitizeObject(sanitized.state);
    }
    
    return sanitized;
  }

  private sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }
    
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      if (this.sensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  private getLogColor(level: string): string {
    switch (level) {
      case 'debug': return '#6B7280';
      case 'info': return '#3B82F6';
      case 'warn': return '#F59E0B';
      case 'error': return '#EF4444';
      default: return '#000000';
    }
  }

  getLogs(level?: string, limit = 100): LogEntry[] {
    let filteredLogs = this.logs;
    
    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }
    
    return filteredLogs.slice(0, limit);
  }

  clearLogs() {
    this.logs = [];
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

const logger = new Logger();

export const loggingMiddleware: Middleware = (store) => (next) => (action) => {
  const startTime = Date.now();
  const stateBefore = store.getState();
  
  try {
    // Execute action
    const result = next(action);
    const endTime = Date.now();
    const stateAfter = store.getState();
    const duration = endTime - startTime;
    
    // Determine log level based on action type
    let level: LogEntry['level'] = 'info';
    if (action.type.includes('rejected')) {
      level = 'error';
    } else if (action.type.includes('pending')) {
      level = 'debug';
    } else if (action.type.includes('fulfilled')) {
      level = 'info';
    }
    
    // Create log entry
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      action: action.type,
      payload: action.payload,
      duration,
      userId: (stateAfter as any).auth?.user?.id,
    };
    
    // Add state diff for important actions
    if (shouldLogStateDiff(action.type)) {
      logEntry.state = {
        before: getRelevantState(stateBefore, action.type),
        after: getRelevantState(stateAfter, action.type),
      };
    }
    
    logger.log(logEntry);
    
    // Create audit log for important actions
    if (shouldCreateAuditLog(action.type)) {
      const auditLog = createAuditLogFromAction(action, stateAfter, duration);
      if (auditLog) {
        store.dispatch(addAuditLog(auditLog));
      }
    }
    
    return result;
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Log error
    logger.log({
      timestamp: new Date().toISOString(),
      level: 'error',
      action: action.type,
      payload: action.payload,
      duration,
      error: error instanceof Error ? error.message : String(error),
      userId: (stateBefore as any).auth?.user?.id,
    });
    
    throw error;
  }
};

function shouldLogStateDiff(actionType: string): boolean {
  const importantActions = [
    'auth/login',
    'auth/logout',
    'users/create',
    'users/update',
    'users/delete',
    'settings/update',
    'theme/setTheme',
  ];
  
  return importantActions.some(action => actionType.includes(action));
}

function shouldCreateAuditLog(actionType: string): boolean {
  const auditableActions = [
    'login',
    'logout',
    'create',
    'update',
    'delete',
    'export',
    'settings',
  ];
  
  return auditableActions.some(action => actionType.toLowerCase().includes(action));
}

function getRelevantState(state: any, actionType: string): any {
  // Return only relevant parts of state based on action type
  if (actionType.includes('auth')) {
    return {
      isAuthenticated: state.auth?.isAuthenticated,
      user: state.auth?.user ? {
        id: state.auth.user.id,
        email: state.auth.user.email,
        role: state.auth.user.role,
      } : null,
    };
  }
  
  if (actionType.includes('users')) {
    return {
      userCount: state.users?.users?.length || 0,
      selectedUsers: state.users?.selectedUsers?.length || 0,
    };
  }
  
  if (actionType.includes('theme')) {
    return state.theme;
  }
  
  if (actionType.includes('settings')) {
    return state.settings;
  }
  
  return null;
}

function createAuditLogFromAction(action: any, state: any, duration: number): AuditLog | null {
  const currentUser = state.auth?.user;
  if (!currentUser) return null;
  
  const actionType = action.type;
  let auditAction = 'unknown';
  let resource = 'unknown';
  let resourceId: string | undefined;
  let oldValues: Record<string, any> | undefined;
  let newValues: Record<string, any> | undefined;
  
  // Parse action type to extract audit information
  if (actionType.includes('login')) {
    auditAction = 'login';
    resource = 'auth';
    newValues = { timestamp: new Date().toISOString() };
  } else if (actionType.includes('logout')) {
    auditAction = 'logout';
    resource = 'auth';
    newValues = { timestamp: new Date().toISOString() };
  } else if (actionType.includes('createUser')) {
    auditAction = 'create';
    resource = 'user';
    newValues = action.payload;
  } else if (actionType.includes('updateUser')) {
    auditAction = 'update';
    resource = 'user';
    resourceId = action.payload?.id;
    newValues = action.payload?.data;
  } else if (actionType.includes('deleteUser')) {
    auditAction = 'delete';
    resource = 'user';
    resourceId = action.payload;
  } else if (actionType.includes('updateSettings')) {
    auditAction = 'update';
    resource = 'settings';
    newValues = action.payload;
  } else if (actionType.includes('export')) {
    auditAction = 'export';
    resource = 'data';
    newValues = { format: action.payload?.format, filters: action.payload?.filters };
  }
  
  return {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    userId: currentUser.id,
    action: auditAction,
    resource,
    resourceId,
    oldValues,
    newValues,
    ip: 'client-side', // Would be filled by server
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    success: !actionType.includes('rejected'),
    error: actionType.includes('rejected') ? action.payload : undefined,
  };
}

// Export logger instance for external use
export { logger };

// Helper functions for components
export const loggerHelpers = {
  debug: (message: string, data?: any) => {
    logger.log({
      timestamp: new Date().toISOString(),
      level: 'debug',
      action: 'manual_log',
      payload: { message, data },
    });
  },
  
  info: (message: string, data?: any) => {
    logger.log({
      timestamp: new Date().toISOString(),
      level: 'info',
      action: 'manual_log',
      payload: { message, data },
    });
  },
  
  warn: (message: string, data?: any) => {
    logger.log({
      timestamp: new Date().toISOString(),
      level: 'warn',
      action: 'manual_log',
      payload: { message, data },
    });
  },
  
  error: (message: string, error?: any) => {
    logger.log({
      timestamp: new Date().toISOString(),
      level: 'error',
      action: 'manual_log',
      payload: { message },
      error: error instanceof Error ? error.message : String(error),
    });
  },
  
  getLogs: logger.getLogs.bind(logger),
  clearLogs: logger.clearLogs.bind(logger),
  exportLogs: logger.exportLogs.bind(logger),
};
