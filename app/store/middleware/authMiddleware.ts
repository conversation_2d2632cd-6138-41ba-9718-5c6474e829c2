/**
 * Authentication Middleware
 * Handles auth-related side effects and security checks
 */

import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import { loginAsync, logoutAsync, refreshTokenAsync } from '../slices/authSlice';
import { addNotification } from '../slices/notificationSlice';
import { addSecurityEvent } from '../slices/auditSlice';
import { secureStorage } from '../../utils/secureStorage';
import { SECURITY_CONFIG } from '../../config/security';

export const authMiddleware = createListenerMiddleware();

// Listen for successful login
authMiddleware.startListening({
  matcher: isAnyOf(loginAsync.fulfilled),
  effect: async (action, listenerApi) => {
    const { user, tokens } = action.payload;
    
    // Store tokens securely
    secureStorage.setAuthTokens(tokens);
    
    // Add success notification
    listenerApi.dispatch(
      addNotification({
        id: Date.now().toString(),
        type: 'success',
        title: 'Welcome back!',
        message: `Successfully logged in as ${user.firstName} ${user.lastName}`,
        read: false,
        createdAt: new Date().toISOString(),
      })
    );
    
    // Log security event
    listenerApi.dispatch(
      addSecurityEvent({
        id: Date.now().toString(),
        type: 'login_success',
        userId: user.id,
        ip: 'client-side',
        userAgent: navigator.userAgent,
        details: {
          email: user.email,
          role: user.role,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
        severity: 'low',
      })
    );
    
    // Clear any lockout state
    secureStorage.clearLockout();
    secureStorage.clearLoginAttempts();
  },
});

// Listen for failed login
authMiddleware.startListening({
  matcher: isAnyOf(loginAsync.rejected),
  effect: async (action, listenerApi) => {
    const error = action.payload as string;
    
    // Add error notification
    listenerApi.dispatch(
      addNotification({
        id: Date.now().toString(),
        type: 'error',
        title: 'Login Failed',
        message: error || 'Invalid credentials',
        read: false,
        createdAt: new Date().toISOString(),
      })
    );
    
    // Log security event
    listenerApi.dispatch(
      addSecurityEvent({
        id: Date.now().toString(),
        type: 'login_failed',
        ip: 'client-side',
        userAgent: navigator.userAgent,
        details: {
          error,
          timestamp: new Date().toISOString(),
          attempts: secureStorage.getLoginAttempts() + 1,
        },
        timestamp: new Date().toISOString(),
        severity: 'medium',
      })
    );
    
    // Handle account lockout
    const attempts = secureStorage.getLoginAttempts() + 1;
    if (attempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
      const lockoutUntil = Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION;
      secureStorage.setLockoutUntil(lockoutUntil);
      
      listenerApi.dispatch(
        addNotification({
          id: Date.now().toString(),
          type: 'warning',
          title: 'Account Locked',
          message: `Too many failed attempts. Account locked for ${SECURITY_CONFIG.LOCKOUT_DURATION / 60000} minutes.`,
          read: false,
          createdAt: new Date().toISOString(),
        })
      );
      
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'account_locked',
          ip: 'client-side',
          userAgent: navigator.userAgent,
          details: {
            attempts,
            lockoutUntil: new Date(lockoutUntil).toISOString(),
          },
          timestamp: new Date().toISOString(),
          severity: 'high',
        })
      );
    }
  },
});

// Listen for logout
authMiddleware.startListening({
  matcher: isAnyOf(logoutAsync.fulfilled),
  effect: async (action, listenerApi) => {
    // Clear all stored data
    secureStorage.clearAuthTokens();
    secureStorage.clearCSRFToken();
    
    // Add notification
    listenerApi.dispatch(
      addNotification({
        id: Date.now().toString(),
        type: 'info',
        title: 'Logged Out',
        message: 'You have been successfully logged out',
        read: false,
        createdAt: new Date().toISOString(),
      })
    );
    
    // Log security event
    listenerApi.dispatch(
      addSecurityEvent({
        id: Date.now().toString(),
        type: 'logout',
        ip: 'client-side',
        userAgent: navigator.userAgent,
        details: {
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
        severity: 'low',
      })
    );
    
    // Redirect to login page
    setTimeout(() => {
      window.location.href = '/fghasfdfanbrmna'; // Obfuscated login path
    }, 1000);
  },
});

// Listen for token refresh
authMiddleware.startListening({
  matcher: isAnyOf(refreshTokenAsync.fulfilled),
  effect: async (action, listenerApi) => {
    const tokens = action.payload;
    
    // Store new tokens
    secureStorage.setAuthTokens(tokens);
    
    // Log security event
    listenerApi.dispatch(
      addSecurityEvent({
        id: Date.now().toString(),
        type: 'login_success',
        ip: 'client-side',
        userAgent: navigator.userAgent,
        details: {
          type: 'token_refresh',
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
        severity: 'low',
      })
    );
  },
});

// Listen for failed token refresh
authMiddleware.startListening({
  matcher: isAnyOf(refreshTokenAsync.rejected),
  effect: async (action, listenerApi) => {
    // Clear tokens and redirect to login
    secureStorage.clearAuthTokens();
    
    listenerApi.dispatch(
      addNotification({
        id: Date.now().toString(),
        type: 'warning',
        title: 'Session Expired',
        message: 'Your session has expired. Please log in again.',
        read: false,
        createdAt: new Date().toISOString(),
      })
    );
    
    // Log security event
    listenerApi.dispatch(
      addSecurityEvent({
        id: Date.now().toString(),
        type: 'login_failed',
        ip: 'client-side',
        userAgent: navigator.userAgent,
        details: {
          type: 'token_refresh_failed',
          error: action.payload,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
        severity: 'medium',
      })
    );
    
    // Redirect to login
    setTimeout(() => {
      window.location.href = '/fghasfdfanbrmna';
    }, 2000);
  },
});

// Session timeout checker
let sessionTimeoutInterval: NodeJS.Timeout;

authMiddleware.startListening({
  matcher: isAnyOf(loginAsync.fulfilled, refreshTokenAsync.fulfilled),
  effect: async (action, listenerApi) => {
    // Clear existing interval
    if (sessionTimeoutInterval) {
      clearInterval(sessionTimeoutInterval);
    }
    
    // Don't set timeout if remember me is enabled
    const rememberMe = secureStorage.getRememberMe();
    if (rememberMe) return;
    
    // Set up session timeout checker
    sessionTimeoutInterval = setInterval(() => {
      const state = listenerApi.getState() as any;
      const sessionTimeout = state.auth.sessionTimeout;
      
      if (sessionTimeout && Date.now() > sessionTimeout) {
        // Session expired
        listenerApi.dispatch(logoutAsync());
        
        listenerApi.dispatch(
          addSecurityEvent({
            id: Date.now().toString(),
            type: 'login_failed',
            ip: 'client-side',
            userAgent: navigator.userAgent,
            details: {
              type: 'session_timeout',
              timestamp: new Date().toISOString(),
            },
            timestamp: new Date().toISOString(),
            severity: 'low',
          })
        );
        
        clearInterval(sessionTimeoutInterval);
      }
    }, 60000); // Check every minute
  },
});

// Clear session timeout on logout
authMiddleware.startListening({
  matcher: isAnyOf(logoutAsync.fulfilled),
  effect: async () => {
    if (sessionTimeoutInterval) {
      clearInterval(sessionTimeoutInterval);
    }
  },
});
