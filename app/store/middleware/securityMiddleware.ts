/**
 * Security Middleware
 * Monitors for suspicious activities and security violations
 */

import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import { addSecurityEvent } from '../slices/auditSlice';
import { addNotification } from '../slices/notificationSlice';
import { SECURITY_CONFIG } from '../../config/security';

export const securityMiddleware = createListenerMiddleware();

// Track suspicious activities
const suspiciousActivityTracker = {
  rapidRequests: new Map<string, number[]>(),
  failedAttempts: new Map<string, number>(),
  unusualPatterns: new Map<string, any[]>(),
};

// Monitor for rapid API requests (potential DoS)
securityMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('/pending') && action.type.includes('api/');
  },
  effect: async (action, listenerApi) => {
    const now = Date.now();
    const actionType = action.type;
    const windowMs = 60000; // 1 minute window
    const maxRequests = 100; // Max requests per minute
    
    // Track requests
    if (!suspiciousActivityTracker.rapidRequests.has(actionType)) {
      suspiciousActivityTracker.rapidRequests.set(actionType, []);
    }
    
    const requests = suspiciousActivityTracker.rapidRequests.get(actionType)!;
    
    // Remove old requests outside the window
    const recentRequests = requests.filter(timestamp => now - timestamp < windowMs);
    recentRequests.push(now);
    
    suspiciousActivityTracker.rapidRequests.set(actionType, recentRequests);
    
    // Check if threshold exceeded
    if (recentRequests.length > maxRequests) {
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'suspicious_activity',
          ip: 'client-side',
          userAgent: navigator.userAgent,
          details: {
            type: 'rapid_requests',
            actionType,
            requestCount: recentRequests.length,
            timeWindow: windowMs,
            threshold: maxRequests,
          },
          timestamp: new Date().toISOString(),
          severity: 'high',
        })
      );
      
      listenerApi.dispatch(
        addNotification({
          id: Date.now().toString(),
          type: 'warning',
          title: 'Unusual Activity Detected',
          message: 'High number of requests detected. Please slow down.',
          read: false,
          createdAt: new Date().toISOString(),
        })
      );
    }
  },
});

// Monitor for failed authentication attempts
securityMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('rejected') && 
           (action.type.includes('login') || action.type.includes('auth'));
  },
  effect: async (action, listenerApi) => {
    const ip = 'client-side'; // Would be actual IP in production
    const now = Date.now();
    
    // Track failed attempts per IP
    const currentAttempts = suspiciousActivityTracker.failedAttempts.get(ip) || 0;
    suspiciousActivityTracker.failedAttempts.set(ip, currentAttempts + 1);
    
    // Check for brute force attack
    if (currentAttempts + 1 > SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS * 2) {
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'suspicious_activity',
          ip,
          userAgent: navigator.userAgent,
          details: {
            type: 'brute_force_attempt',
            attempts: currentAttempts + 1,
            threshold: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS * 2,
            actionType: action.type,
          },
          timestamp: new Date().toISOString(),
          severity: 'critical',
        })
      );
    }
    
    // Reset counter after 1 hour
    setTimeout(() => {
      suspiciousActivityTracker.failedAttempts.delete(ip);
    }, 3600000);
  },
});

// Monitor for privilege escalation attempts
securityMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('updateUser') || 
           action.type.includes('updateRole') ||
           action.type.includes('updateSettings');
  },
  effect: async (action, listenerApi) => {
    const state = listenerApi.getState() as any;
    const currentUser = state.auth.user;
    
    if (!currentUser) return;
    
    // Check if user is trying to modify higher-level users or settings
    const isPrivilegeEscalation = 
      (action.type.includes('updateUser') && action.payload?.role === 'super_admin') ||
      (action.type.includes('updateRole') && currentUser.role !== 'super_admin') ||
      (action.type.includes('updateSettings') && currentUser.role !== 'admin');
    
    if (isPrivilegeEscalation) {
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'suspicious_activity',
          userId: currentUser.id,
          ip: 'client-side',
          userAgent: navigator.userAgent,
          details: {
            type: 'privilege_escalation_attempt',
            actionType: action.type,
            userRole: currentUser.role,
            attemptedAction: action.payload,
          },
          timestamp: new Date().toISOString(),
          severity: 'critical',
        })
      );
      
      listenerApi.dispatch(
        addNotification({
          id: Date.now().toString(),
          type: 'error',
          title: 'Unauthorized Action',
          message: 'You do not have permission to perform this action.',
          read: false,
          createdAt: new Date().toISOString(),
        })
      );
    }
  },
});

// Monitor for data exfiltration attempts
securityMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('export') || 
           action.type.includes('download') ||
           (action.type.includes('getUsers') && action.payload?.limit > 1000);
  },
  effect: async (action, listenerApi) => {
    const state = listenerApi.getState() as any;
    const currentUser = state.auth.user;
    
    if (!currentUser) return;
    
    // Check for large data requests
    const isLargeRequest = 
      (action.type.includes('getUsers') && action.payload?.limit > 1000) ||
      action.type.includes('export');
    
    if (isLargeRequest) {
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'data_access',
          userId: currentUser.id,
          ip: 'client-side',
          userAgent: navigator.userAgent,
          details: {
            type: 'large_data_request',
            actionType: action.type,
            requestSize: action.payload?.limit || 'export',
            userRole: currentUser.role,
          },
          timestamp: new Date().toISOString(),
          severity: 'medium',
        })
      );
    }
  },
});

// Monitor for unusual navigation patterns
let navigationHistory: string[] = [];

securityMiddleware.startListening({
  predicate: (action) => {
    return action.type === '@@router/LOCATION_CHANGE' || 
           window.location.pathname !== navigationHistory[navigationHistory.length - 1];
  },
  effect: async (action, listenerApi) => {
    const currentPath = window.location.pathname;
    navigationHistory.push(currentPath);
    
    // Keep only last 50 navigation entries
    if (navigationHistory.length > 50) {
      navigationHistory = navigationHistory.slice(-50);
    }
    
    // Check for suspicious patterns
    const recentPaths = navigationHistory.slice(-10);
    const uniquePaths = new Set(recentPaths);
    
    // Rapid navigation between different admin sections
    if (uniquePaths.size > 7 && recentPaths.length === 10) {
      const state = listenerApi.getState() as any;
      const currentUser = state.auth.user;
      
      listenerApi.dispatch(
        addSecurityEvent({
          id: Date.now().toString(),
          type: 'suspicious_activity',
          userId: currentUser?.id,
          ip: 'client-side',
          userAgent: navigator.userAgent,
          details: {
            type: 'rapid_navigation',
            paths: recentPaths,
            uniquePathCount: uniquePaths.size,
          },
          timestamp: new Date().toISOString(),
          severity: 'low',
        })
      );
    }
    
    // Access to admin paths without proper role
    if (currentPath.startsWith('/admin') || currentPath.startsWith('/sa/')) {
      const state = listenerApi.getState() as any;
      const currentUser = state.auth.user;
      
      if (!currentUser || 
          (currentPath.startsWith('/sa/') && currentUser.role !== 'super_admin') ||
          (currentPath.startsWith('/admin') && !['admin', 'super_admin'].includes(currentUser.role))) {
        
        listenerApi.dispatch(
          addSecurityEvent({
            id: Date.now().toString(),
            type: 'permission_denied',
            userId: currentUser?.id,
            ip: 'client-side',
            userAgent: navigator.userAgent,
            details: {
              type: 'unauthorized_path_access',
              path: currentPath,
              userRole: currentUser?.role || 'anonymous',
            },
            timestamp: new Date().toISOString(),
            severity: 'high',
          })
        );
      }
    }
  },
});

// Monitor for console manipulation attempts
if (typeof window !== 'undefined') {
  const originalConsole = { ...console };
  
  // Override console methods to detect manipulation
  ['log', 'warn', 'error', 'info'].forEach(method => {
    (console as any)[method] = (...args: any[]) => {
      // Check for suspicious console usage
      const message = args.join(' ');
      if (message.includes('token') || 
          message.includes('password') || 
          message.includes('secret') ||
          message.includes('localStorage') ||
          message.includes('sessionStorage')) {
        
        // Log security event (but don't dispatch to avoid infinite loop)
        originalConsole.warn('Suspicious console activity detected:', message);
      }
      
      return (originalConsole as any)[method](...args);
    };
  });
}

// Clean up tracking data periodically
setInterval(() => {
  const now = Date.now();
  const cleanupAge = 3600000; // 1 hour
  
  // Clean up rapid requests tracking
  for (const [key, timestamps] of suspiciousActivityTracker.rapidRequests.entries()) {
    const recent = timestamps.filter(t => now - t < cleanupAge);
    if (recent.length === 0) {
      suspiciousActivityTracker.rapidRequests.delete(key);
    } else {
      suspiciousActivityTracker.rapidRequests.set(key, recent);
    }
  }
  
  // Clean up navigation history
  if (navigationHistory.length > 100) {
    navigationHistory = navigationHistory.slice(-50);
  }
}, 300000); // Clean up every 5 minutes
