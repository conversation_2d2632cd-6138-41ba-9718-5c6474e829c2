/**
 * Theme Slice
 * Manages application theme and UI preferences
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Initial state
const initialState: ThemeConfig = {
  mode: 'light',
  primaryColor: '#1890ff',
  borderRadius: 6,
  fontSize: 14,
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  compactMode: false,
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Partial<ThemeConfig>>) => {
      Object.assign(state, action.payload);
    },
    
    toggleTheme: (state) => {
      state.mode = state.mode === 'light' ? 'dark' : 'light';
    },
    
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      state.primaryColor = action.payload;
    },
    
    setCompactMode: (state, action: PayloadAction<boolean>) => {
      state.compactMode = action.payload;
    },
    
    setFontSize: (state, action: PayloadAction<number>) => {
      state.fontSize = Math.max(12, Math.min(20, action.payload));
    },
    
    setBorderRadius: (state, action: PayloadAction<number>) => {
      state.borderRadius = Math.max(0, Math.min(20, action.payload));
    },
    
    resetTheme: () => initialState,
  },
});

export const {
  setTheme,
  toggleTheme,
  setPrimaryColor,
  setCompactMode,
  setFontSize,
  setBorderRadius,
  resetTheme,
} = themeSlice.actions;

export default themeSlice.reducer;
