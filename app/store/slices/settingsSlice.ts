/**
 * Settings Slice
 * Manages system and user settings
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState: SystemSettings = {
  siteName: 'Admin Panel',
  siteDescription: 'Production-ready admin panel with comprehensive security',
  logo: '/logo.png',
  favicon: '/favicon.ico',
  timezone: 'UTC',
  dateFormat: 'YYYY-MM-DD',
  currency: 'USD',
  language: 'en',
  maintenanceMode: false,
  registrationEnabled: true,
  emailVerificationRequired: true,
  twoFactorRequired: false,
  sessionTimeout: 3600000, // 1 hour
  maxLoginAttempts: 5,
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    preventReuse: 5,
    expirationDays: 90,
  },
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateSettings: (state, action: PayloadAction<Partial<SystemSettings>>) => {
      Object.assign(state, action.payload);
    },
    
    updatePasswordPolicy: (state, action: PayloadAction<Partial<PasswordPolicy>>) => {
      Object.assign(state.passwordPolicy, action.payload);
    },
    
    toggleMaintenanceMode: (state) => {
      state.maintenanceMode = !state.maintenanceMode;
    },
    
    toggleRegistration: (state) => {
      state.registrationEnabled = !state.registrationEnabled;
    },
    
    toggleEmailVerification: (state) => {
      state.emailVerificationRequired = !state.emailVerificationRequired;
    },
    
    toggleTwoFactor: (state) => {
      state.twoFactorRequired = !state.twoFactorRequired;
    },
    
    setSessionTimeout: (state, action: PayloadAction<number>) => {
      state.sessionTimeout = action.payload;
    },
    
    setMaxLoginAttempts: (state, action: PayloadAction<number>) => {
      state.maxLoginAttempts = Math.max(1, Math.min(10, action.payload));
    },
    
    resetSettings: () => initialState,
  },
});

export const {
  updateSettings,
  updatePasswordPolicy,
  toggleMaintenanceMode,
  toggleRegistration,
  toggleEmailVerification,
  toggleTwoFactor,
  setSessionTimeout,
  setMaxLoginAttempts,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
