/**
 * Authentication Slice
 * Manages authentication state with security features
 */

import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { authService } from "../../services/auth.service";
import { secureStorage } from "../../utils/secureStorage";
import { SECURITY_CONFIG } from "../../config/security";

// Fix import issue by adding type import
import type { RootState } from "../index";

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  sessionTimeout: null,
  rememberMe: false,
  loginAttempts: 0,
  isLocked: false,
  lockoutUntil: null,
};

// Async thunks
export const loginAsync = createAsyncThunk(
  "auth/login",
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const result = await authService.login(credentials);
      return result;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Lo<PERSON> failed"
      );
    }
  }
);

export const registerAsync = createAsyncThunk(
  "auth/register",
  async (userData: RegisterData, { rejectWithValue }) => {
    try {
      const result = await authService.register(userData);
      return result;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Registration failed"
      );
    }
  }
);

export const refreshTokenAsync = createAsyncThunk(
  "auth/refreshToken",
  async (_, { rejectWithValue }) => {
    try {
      const tokens = await authService.refreshToken();
      return tokens;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Token refresh failed"
      );
    }
  }
);

export const getCurrentUserAsync = createAsyncThunk(
  "auth/getCurrentUser",
  async (_, { rejectWithValue }) => {
    try {
      const user = await authService.getCurrentUser();
      return user;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Failed to get user"
      );
    }
  }
);

export const changePasswordAsync = createAsyncThunk(
  "auth/changePassword",
  async (
    {
      currentPassword,
      newPassword,
    }: { currentPassword: string; newPassword: string },
    { rejectWithValue }
  ) => {
    try {
      await authService.changePassword(currentPassword, newPassword);
      return true;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Password change failed"
      );
    }
  }
);

export const logoutAsync = createAsyncThunk(
  "auth/logout",
  async (_, { dispatch }) => {
    try {
      await authService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Always clear local state
      dispatch(authSlice.actions.clearAuthData());
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Synchronous actions
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },

    loginSuccess: (
      state,
      action: PayloadAction<{ user: User; tokens: AuthTokens }>
    ) => {
      const { user, tokens } = action.payload;
      state.user = user;
      state.token = tokens.accessToken;
      state.refreshToken = tokens.refreshToken;
      state.isAuthenticated = true;
      state.isLoading = false;
      state.error = null;
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockoutUntil = null;
      state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
    },

    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
      state.loginAttempts += 1;

      // Check if account should be locked
      if (state.loginAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
        state.isLocked = true;
        state.lockoutUntil = Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION;
      }
    },

    logout: (state) => {
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      state.sessionTimeout = null;
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockoutUntil = null;
    },

    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },

    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },

    setTokens: (state, action: PayloadAction<AuthTokens>) => {
      const tokens = action.payload;
      state.token = tokens.accessToken;
      state.refreshToken = tokens.refreshToken;
      state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
    },

    clearError: (state) => {
      state.error = null;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setRememberMe: (state, action: PayloadAction<boolean>) => {
      state.rememberMe = action.payload;
      secureStorage.setRememberMe(action.payload);
    },

    clearLockout: (state) => {
      state.isLocked = false;
      state.lockoutUntil = null;
      state.loginAttempts = 0;
    },

    updateSessionTimeout: (state) => {
      if (state.isAuthenticated && !state.rememberMe) {
        state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
      }
    },

    clearAuthData: (state) => {
      // Clear all auth-related data
      Object.assign(state, initialState);
      secureStorage.clearAuthTokens();
    },

    // Security actions
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
      if (state.loginAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
        state.isLocked = true;
        state.lockoutUntil = Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION;
      }
    },

    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockoutUntil = null;
    },

    // Session management
    extendSession: (state) => {
      if (state.isAuthenticated) {
        state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
      }
    },

    checkSessionExpiry: (state) => {
      if (
        state.sessionTimeout &&
        Date.now() > state.sessionTimeout &&
        !state.rememberMe
      ) {
        // Session expired
        Object.assign(state, initialState);
        secureStorage.clearAuthTokens();
      }
    },
  },

  extraReducers: (builder) => {
    // Login async
    builder
      .addCase(loginAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        const { user, tokens } = action.payload;
        state.user = user;
        state.token = tokens.accessToken;
        state.refreshToken = tokens.refreshToken;
        state.isAuthenticated = true;
        state.isLoading = false;
        state.error = null;
        state.loginAttempts = 0;
        state.isLocked = false;
        state.lockoutUntil = null;
        state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;

        if (state.loginAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
          state.isLocked = true;
          state.lockoutUntil = Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION;
        }
      });

    // Register async
    builder
      .addCase(registerAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        const { user, tokens } = action.payload;
        state.user = user;
        state.token = tokens.accessToken;
        state.refreshToken = tokens.refreshToken;
        state.isAuthenticated = true;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(registerAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Refresh token async
    builder
      .addCase(refreshTokenAsync.fulfilled, (state, action) => {
        const tokens = action.payload;
        state.token = tokens.accessToken;
        state.refreshToken = tokens.refreshToken;
        state.sessionTimeout = Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT;
      })
      .addCase(refreshTokenAsync.rejected, (state) => {
        // Token refresh failed, logout user
        Object.assign(state, initialState);
        secureStorage.clearAuthTokens();
      });

    // Get current user async
    builder
      .addCase(getCurrentUserAsync.fulfilled, (state, action) => {
        state.user = action.payload;
      })
      .addCase(getCurrentUserAsync.rejected, (state) => {
        // Failed to get user, might be unauthorized
        if (state.isAuthenticated) {
          Object.assign(state, initialState);
          secureStorage.clearAuthTokens();
        }
      });

    // Change password async
    builder
      .addCase(changePasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(changePasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(changePasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Logout async
    builder.addCase(logoutAsync.fulfilled, (state) => {
      Object.assign(state, initialState);
    });
  },
});

// Export actions
export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  setUser,
  updateUser,
  setTokens,
  clearError,
  setLoading,
  setRememberMe,
  clearLockout,
  updateSessionTimeout,
  clearAuthData,
  incrementLoginAttempts,
  resetLoginAttempts,
  extendSession,
  checkSessionExpiry,
} = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;
export const selectError = (state: { auth: AuthState }) => state.auth.error;
export const selectIsLocked = (state: { auth: AuthState }) =>
  state.auth.isLocked;
export const selectLoginAttempts = (state: { auth: AuthState }) =>
  state.auth.loginAttempts;

export default authSlice.reducer;
