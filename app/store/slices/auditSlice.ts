/**
 * Audit Slice
 * Manages audit logs and security events
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

interface AuditState {
  logs: AuditLog[];
  securityEvents: SecurityEvent[];
  isLoading: boolean;
  error: string | null;
  filters: {
    dateRange: [string, string] | null;
    userId: string;
    action: string;
    resource: string;
    severity: SecurityEvent['severity'] | '';
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

const initialState: AuditState = {
  logs: [],
  securityEvents: [],
  isLoading: false,
  error: null,
  filters: {
    dateRange: null,
    userId: '',
    action: '',
    resource: '',
    severity: '',
  },
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 50,
  },
};

// Async thunks
export const fetchAuditLogsAsync = createAsyncThunk(
  'audit/fetchLogs',
  async (params: SearchParams, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/audit/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch audit logs');
      }
      
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch audit logs');
    }
  }
);

export const fetchSecurityEventsAsync = createAsyncThunk(
  'audit/fetchSecurityEvents',
  async (params: SearchParams, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/audit/security-events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch security events');
      }
      
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch security events');
    }
  }
);

export const exportAuditLogsAsync = createAsyncThunk(
  'audit/exportLogs',
  async (params: { format: 'csv' | 'json' | 'pdf'; filters: any }, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/audit/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        throw new Error('Failed to export audit logs');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${params.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      return true;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to export audit logs');
    }
  }
);

const auditSlice = createSlice({
  name: 'audit',
  initialState,
  reducers: {
    addAuditLog: (state, action: PayloadAction<AuditLog>) => {
      state.logs.unshift(action.payload);
      
      // Limit to 1000 logs in memory
      if (state.logs.length > 1000) {
        state.logs.splice(1000);
      }
    },
    
    addSecurityEvent: (state, action: PayloadAction<SecurityEvent>) => {
      state.securityEvents.unshift(action.payload);
      
      // Limit to 500 events in memory
      if (state.securityEvents.length > 500) {
        state.securityEvents.splice(500);
      }
    },
    
    setAuditLogs: (state, action: PayloadAction<AuditLog[]>) => {
      state.logs = action.payload;
    },
    
    setSecurityEvents: (state, action: PayloadAction<SecurityEvent[]>) => {
      state.securityEvents = action.payload;
    },
    
    clearAuditLogs: (state) => {
      state.logs = [];
    },
    
    clearSecurityEvents: (state) => {
      state.securityEvents = [];
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<AuditState['filters']>>) => {
      Object.assign(state.filters, action.payload);
    },
    
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    setPagination: (state, action: PayloadAction<Partial<AuditState['pagination']>>) => {
      Object.assign(state.pagination, action.payload);
    },
    
    // Bulk operations
    deleteOldLogs: (state, action: PayloadAction<string>) => {
      const cutoffDate = action.payload;
      state.logs = state.logs.filter(log => log.timestamp > cutoffDate);
      state.securityEvents = state.securityEvents.filter(event => event.timestamp > cutoffDate);
    },
    
    markEventAsReviewed: (state, action: PayloadAction<string>) => {
      const event = state.securityEvents.find(e => e.id === action.payload);
      if (event) {
        event.details = { ...event.details, reviewed: true, reviewedAt: new Date().toISOString() };
      }
    },
  },
  
  extraReducers: (builder) => {
    // Fetch audit logs
    builder
      .addCase(fetchAuditLogsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAuditLogsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.logs = action.payload.data;
        state.pagination = action.payload.meta.pagination;
      })
      .addCase(fetchAuditLogsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Fetch security events
    builder
      .addCase(fetchSecurityEventsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSecurityEventsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.securityEvents = action.payload.data;
        state.pagination = action.payload.meta.pagination;
      })
      .addCase(fetchSecurityEventsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Export audit logs
    builder
      .addCase(exportAuditLogsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(exportAuditLogsAsync.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(exportAuditLogsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addAuditLog,
  addSecurityEvent,
  setAuditLogs,
  setSecurityEvents,
  clearAuditLogs,
  clearSecurityEvents,
  setLoading,
  setError,
  setFilters,
  clearFilters,
  setPagination,
  deleteOldLogs,
  markEventAsReviewed,
} = auditSlice.actions;

// Helper functions for creating audit logs
export const createAuditLog = (
  userId: string,
  action: string,
  resource: string,
  resourceId?: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>
): AuditLog => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  userId,
  action,
  resource,
  resourceId,
  oldValues,
  newValues,
  ip: 'client-side', // Would be filled by server
  userAgent: navigator.userAgent,
  timestamp: new Date().toISOString(),
  success: true,
});

export const createSecurityEvent = (
  type: SecurityEventType,
  severity: SecurityEvent['severity'],
  details: Record<string, any>,
  userId?: string
): SecurityEvent => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  type,
  userId,
  ip: 'client-side', // Would be filled by server
  userAgent: navigator.userAgent,
  details,
  timestamp: new Date().toISOString(),
  severity,
});

export default auditSlice.reducer;
