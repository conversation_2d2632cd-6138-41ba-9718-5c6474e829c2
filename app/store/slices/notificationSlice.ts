/**
 * Notification Slice
 * Manages in-app notifications and alerts
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface NotificationState {
  items: Notification[];
  unreadCount: number;
  settings: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
  };
}

const initialState: NotificationState = {
  items: [],
  unreadCount: 0,
  settings: {
    enabled: true,
    sound: true,
    desktop: false,
    email: true,
  },
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.items.unshift(action.payload);
      if (!action.payload.read) {
        state.unreadCount += 1;
      }
      
      // Limit to 100 notifications
      if (state.items.length > 100) {
        const removed = state.items.splice(100);
        state.unreadCount -= removed.filter(n => !n.read).length;
      }
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      const index = state.items.findIndex(n => n.id === action.payload);
      if (index !== -1) {
        const notification = state.items[index];
        if (!notification.read) {
          state.unreadCount -= 1;
        }
        state.items.splice(index, 1);
      }
    },
    
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.items.find(n => n.id === action.payload);
      if (notification && !notification.read) {
        notification.read = true;
        state.unreadCount -= 1;
      }
    },
    
    markAllAsRead: (state) => {
      state.items.forEach(notification => {
        notification.read = true;
      });
      state.unreadCount = 0;
    },
    
    clearAll: (state) => {
      state.items = [];
      state.unreadCount = 0;
    },
    
    clearRead: (state) => {
      state.items = state.items.filter(n => !n.read);
    },
    
    updateSettings: (state, action: PayloadAction<Partial<NotificationState['settings']>>) => {
      Object.assign(state.settings, action.payload);
    },
    
    // Bulk operations
    markMultipleAsRead: (state, action: PayloadAction<string[]>) => {
      action.payload.forEach(id => {
        const notification = state.items.find(n => n.id === id);
        if (notification && !notification.read) {
          notification.read = true;
          state.unreadCount -= 1;
        }
      });
    },
    
    removeMultiple: (state, action: PayloadAction<string[]>) => {
      action.payload.forEach(id => {
        const index = state.items.findIndex(n => n.id === id);
        if (index !== -1) {
          const notification = state.items[index];
          if (!notification.read) {
            state.unreadCount -= 1;
          }
          state.items.splice(index, 1);
        }
      });
    },
  },
});

export const {
  addNotification,
  removeNotification,
  markAsRead,
  markAllAsRead,
  clearAll,
  clearRead,
  updateSettings,
  markMultipleAsRead,
  removeMultiple,
} = notificationSlice.actions;

// Helper action creators
export const createNotification = (
  type: Notification['type'],
  title: string,
  message: string,
  actions?: NotificationAction[]
): Notification => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  type,
  title,
  message,
  read: false,
  createdAt: new Date().toISOString(),
  actions,
});

export const notificationHelpers = {
  success: (title: string, message: string, actions?: NotificationAction[]) =>
    createNotification('success', title, message, actions),
  
  error: (title: string, message: string, actions?: NotificationAction[]) =>
    createNotification('error', title, message, actions),
  
  warning: (title: string, message: string, actions?: NotificationAction[]) =>
    createNotification('warning', title, message, actions),
  
  info: (title: string, message: string, actions?: NotificationAction[]) =>
    createNotification('info', title, message, actions),
};

export default notificationSlice.reducer;
