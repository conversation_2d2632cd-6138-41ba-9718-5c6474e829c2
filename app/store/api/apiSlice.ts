/**
 * RTK Query API Slice
 * Centralized API configuration with automatic caching and invalidation
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { secureStorage } from '../../utils/secureStorage';
import { APP_CONFIG } from '../../config/app';
import { SECURITY_CONFIG } from '../../config/security';

// Custom base query with auth and error handling
const baseQueryWithAuth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: APP_CONFIG.api.baseURL,
    timeout: SECURITY_CONFIG.API.timeout,
    prepareHeaders: (headers, { getState }) => {
      // Add auth token
      const tokens = secureStorage.getAuthTokens();
      if (tokens?.accessToken) {
        headers.set('Authorization', `Bearer ${tokens.accessToken}`);
      }
      
      // Add CSRF token
      const csrfToken = secureStorage.getCSRFToken();
      if (csrfToken) {
        headers.set('X-CSRF-Token', csrfToken);
      }
      
      // Add security headers
      headers.set('X-Requested-With', 'XMLHttpRequest');
      headers.set('Content-Type', 'application/json');
      
      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  // Handle token refresh
  if (result.error && result.error.status === 401) {
    try {
      // Try to refresh token
      const refreshResult = await baseQuery(
        {
          url: '/auth/refresh',
          method: 'POST',
          body: {
            refreshToken: secureStorage.getAuthTokens()?.refreshToken,
          },
        },
        api,
        extraOptions
      );

      if (refreshResult.data) {
        const tokens = (refreshResult.data as any).data;
        secureStorage.setAuthTokens(tokens);
        
        // Retry original request
        result = await baseQuery(args, api, extraOptions);
      } else {
        // Refresh failed, logout user
        secureStorage.clearAuthTokens();
        window.location.href = APP_CONFIG.routes.login;
      }
    } catch (error) {
      // Refresh failed, logout user
      secureStorage.clearAuthTokens();
      window.location.href = APP_CONFIG.routes.login;
    }
  }

  return result;
};

// Main API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithAuth,
  tagTypes: [
    'User',
    'Role',
    'Permission',
    'AuditLog',
    'SecurityEvent',
    'Settings',
    'Translation',
    'File',
  ],
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<
      { user: User; tokens: AuthTokens },
      LoginCredentials
    >({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['User'],
    }),

    register: builder.mutation<
      { user: User; tokens: AuthTokens },
      RegisterData
    >({
      query: (userData) => ({
        url: '/auth/register',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
    }),

    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User'],
    }),

    getCurrentUser: builder.query<User, void>({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),

    changePassword: builder.mutation<
      void,
      { currentPassword: string; newPassword: string }
    >({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    // User management endpoints
    getUsers: builder.query<
      { data: User[]; meta: { pagination: PaginationMeta } },
      SearchParams
    >({
      query: (params) => ({
        url: '/users',
        method: 'POST',
        body: params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'User' as const, id })),
              { type: 'User', id: 'LIST' },
            ]
          : [{ type: 'User', id: 'LIST' }],
    }),

    getUser: builder.query<User, string>({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    createUser: builder.mutation<User, Partial<User>>({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: [{ type: 'User', id: 'LIST' }],
    }),

    updateUser: builder.mutation<User, { id: string; data: Partial<User> }>({
      query: ({ id, data }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'User', id }],
    }),

    deleteUser: builder.mutation<void, string>({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    bulkDeleteUsers: builder.mutation<void, string[]>({
      query: (ids) => ({
        url: '/users/bulk-delete',
        method: 'POST',
        body: { ids },
      }),
      invalidatesTags: [{ type: 'User', id: 'LIST' }],
    }),

    // Role management endpoints
    getRoles: builder.query<Role[], void>({
      query: () => '/roles',
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Role' as const, id })),
              { type: 'Role', id: 'LIST' },
            ]
          : [{ type: 'Role', id: 'LIST' }],
    }),

    createRole: builder.mutation<Role, Partial<Role>>({
      query: (roleData) => ({
        url: '/roles',
        method: 'POST',
        body: roleData,
      }),
      invalidatesTags: [{ type: 'Role', id: 'LIST' }],
    }),

    updateRole: builder.mutation<Role, { id: string; data: Partial<Role> }>({
      query: ({ id, data }) => ({
        url: `/roles/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Role', id }],
    }),

    deleteRole: builder.mutation<void, string>({
      query: (id) => ({
        url: `/roles/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'Role', id }],
    }),

    // Audit log endpoints
    getAuditLogs: builder.query<
      { data: AuditLog[]; meta: { pagination: PaginationMeta } },
      SearchParams
    >({
      query: (params) => ({
        url: '/audit/logs',
        method: 'POST',
        body: params,
      }),
      providesTags: [{ type: 'AuditLog', id: 'LIST' }],
    }),

    getSecurityEvents: builder.query<
      { data: SecurityEvent[]; meta: { pagination: PaginationMeta } },
      SearchParams
    >({
      query: (params) => ({
        url: '/audit/security-events',
        method: 'POST',
        body: params,
      }),
      providesTags: [{ type: 'SecurityEvent', id: 'LIST' }],
    }),

    exportAuditLogs: builder.mutation<
      Blob,
      { format: 'csv' | 'json' | 'pdf'; filters: any }
    >({
      query: (params) => ({
        url: '/audit/export',
        method: 'POST',
        body: params,
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Settings endpoints
    getSettings: builder.query<SystemSettings, void>({
      query: () => '/settings',
      providesTags: [{ type: 'Settings', id: 'SYSTEM' }],
    }),

    updateSettings: builder.mutation<SystemSettings, Partial<SystemSettings>>({
      query: (settings) => ({
        url: '/settings',
        method: 'PUT',
        body: settings,
      }),
      invalidatesTags: [{ type: 'Settings', id: 'SYSTEM' }],
    }),

    // Translation endpoints
    getTranslations: builder.query<
      Translation[],
      { language: string; namespace?: string }
    >({
      query: ({ language, namespace }) => ({
        url: '/translations',
        params: { language, namespace },
      }),
      providesTags: [{ type: 'Translation', id: 'LIST' }],
    }),

    updateTranslation: builder.mutation<
      Translation,
      { id: string; value: string }
    >({
      query: ({ id, value }) => ({
        url: `/translations/${id}`,
        method: 'PUT',
        body: { value },
      }),
      invalidatesTags: [{ type: 'Translation', id: 'LIST' }],
    }),

    // File upload endpoints
    uploadFile: builder.mutation<FileUpload, FormData>({
      query: (formData) => ({
        url: '/upload',
        method: 'POST',
        body: formData,
        formData: true,
      }),
      invalidatesTags: [{ type: 'File', id: 'LIST' }],
    }),

    deleteFile: builder.mutation<void, string>({
      query: (id) => ({
        url: `/files/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'File', id }],
    }),
  }),
});

// Export hooks for usage in components
export const {
  // Auth hooks
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useChangePasswordMutation,

  // User hooks
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useBulkDeleteUsersMutation,

  // Role hooks
  useGetRolesQuery,
  useCreateRoleMutation,
  useUpdateRoleMutation,
  useDeleteRoleMutation,

  // Audit hooks
  useGetAuditLogsQuery,
  useGetSecurityEventsQuery,
  useExportAuditLogsMutation,

  // Settings hooks
  useGetSettingsQuery,
  useUpdateSettingsMutation,

  // Translation hooks
  useGetTranslationsQuery,
  useUpdateTranslationMutation,

  // File hooks
  useUploadFileMutation,
  useDeleteFileMutation,
} = apiSlice;
