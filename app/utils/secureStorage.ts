/**
 * Secure Storage Utility
 * Handles encrypted storage of sensitive data
 */

import CryptoJ<PERSON> from 'crypto-js';
import { SECURITY_CONFIG } from '../config/security';

class SecureStorage {
  private encryptionKey: string;
  private prefix: string;

  constructor() {
    this.encryptionKey = SECURITY_CONFIG.SESSION.storage.encryptionKey;
    this.prefix = SECURITY_CONFIG.SESSION.storage.prefix;
  }

  /**
   * Encrypt data before storing
   */
  private encrypt(data: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey).toString();
      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt data after retrieving
   */
  private decrypt(encryptedData: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Get full key with prefix
   */
  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  /**
   * Store encrypted data in localStorage
   */
  setItem(key: string, value: any, encrypt: boolean = true): void {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      const finalValue = encrypt ? this.encrypt(stringValue) : stringValue;
      localStorage.setItem(this.getKey(key), finalValue);
    } catch (error) {
      console.error('Failed to store item:', error);
      throw new Error('Storage operation failed');
    }
  }

  /**
   * Retrieve and decrypt data from localStorage
   */
  getItem<T = any>(key: string, decrypt: boolean = true): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key));
      if (!item) return null;

      const decryptedValue = decrypt ? this.decrypt(item) : item;
      
      // Try to parse as JSON, fallback to string
      try {
        return JSON.parse(decryptedValue);
      } catch {
        return decryptedValue as T;
      }
    } catch (error) {
      console.error('Failed to retrieve item:', error);
      return null;
    }
  }

  /**
   * Remove item from localStorage
   */
  removeItem(key: string): void {
    try {
      localStorage.removeItem(this.getKey(key));
    } catch (error) {
      console.error('Failed to remove item:', error);
    }
  }

  /**
   * Clear all items with our prefix
   */
  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }

  /**
   * Store data in sessionStorage (temporary)
   */
  setSessionItem(key: string, value: any, encrypt: boolean = true): void {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      const finalValue = encrypt ? this.encrypt(stringValue) : stringValue;
      sessionStorage.setItem(this.getKey(key), finalValue);
    } catch (error) {
      console.error('Failed to store session item:', error);
      throw new Error('Session storage operation failed');
    }
  }

  /**
   * Retrieve data from sessionStorage
   */
  getSessionItem<T = any>(key: string, decrypt: boolean = true): T | null {
    try {
      const item = sessionStorage.getItem(this.getKey(key));
      if (!item) return null;

      const decryptedValue = decrypt ? this.decrypt(item) : item;
      
      try {
        return JSON.parse(decryptedValue);
      } catch {
        return decryptedValue as T;
      }
    } catch (error) {
      console.error('Failed to retrieve session item:', error);
      return null;
    }
  }

  /**
   * Remove item from sessionStorage
   */
  removeSessionItem(key: string): void {
    try {
      sessionStorage.removeItem(this.getKey(key));
    } catch (error) {
      console.error('Failed to remove session item:', error);
    }
  }

  /**
   * Store sensitive auth tokens securely
   */
  setAuthTokens(tokens: AuthTokens): void {
    this.setItem('auth_tokens', tokens, true);
  }

  /**
   * Retrieve auth tokens
   */
  getAuthTokens(): AuthTokens | null {
    return this.getItem<AuthTokens>('auth_tokens', true);
  }

  /**
   * Remove auth tokens
   */
  clearAuthTokens(): void {
    this.removeItem('auth_tokens');
    this.removeSessionItem('auth_tokens');
  }

  /**
   * Store user preferences (less sensitive)
   */
  setUserPreferences(preferences: UserPreferences): void {
    this.setItem('user_preferences', preferences, false);
  }

  /**
   * Retrieve user preferences
   */
  getUserPreferences(): UserPreferences | null {
    return this.getItem<UserPreferences>('user_preferences', false);
  }

  /**
   * Store remember me preference
   */
  setRememberMe(remember: boolean): void {
    this.setItem('remember_me', remember, false);
  }

  /**
   * Get remember me preference
   */
  getRememberMe(): boolean {
    return this.getItem<boolean>('remember_me', false) || false;
  }

  /**
   * Store session timeout timestamp
   */
  setSessionTimeout(timestamp: number): void {
    this.setSessionItem('session_timeout', timestamp, false);
  }

  /**
   * Get session timeout timestamp
   */
  getSessionTimeout(): number | null {
    return this.getSessionItem<number>('session_timeout', false);
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(): boolean {
    const timeout = this.getSessionTimeout();
    if (!timeout) return false;
    return Date.now() > timeout;
  }

  /**
   * Store login attempts count
   */
  setLoginAttempts(count: number): void {
    this.setItem('login_attempts', count, false);
  }

  /**
   * Get login attempts count
   */
  getLoginAttempts(): number {
    return this.getItem<number>('login_attempts', false) || 0;
  }

  /**
   * Clear login attempts
   */
  clearLoginAttempts(): void {
    this.removeItem('login_attempts');
  }

  /**
   * Store lockout timestamp
   */
  setLockoutUntil(timestamp: number): void {
    this.setItem('lockout_until', timestamp, false);
  }

  /**
   * Get lockout timestamp
   */
  getLockoutUntil(): number | null {
    return this.getItem<number>('lockout_until', false);
  }

  /**
   * Check if account is locked
   */
  isAccountLocked(): boolean {
    const lockoutUntil = this.getLockoutUntil();
    if (!lockoutUntil) return false;
    return Date.now() < lockoutUntil;
  }

  /**
   * Clear lockout
   */
  clearLockout(): void {
    this.removeItem('lockout_until');
  }

  /**
   * Store CSRF token
   */
  setCSRFToken(token: string): void {
    this.setSessionItem('csrf_token', token, false);
  }

  /**
   * Get CSRF token
   */
  getCSRFToken(): string | null {
    return this.getSessionItem<string>('csrf_token', false);
  }

  /**
   * Clear CSRF token
   */
  clearCSRFToken(): void {
    this.removeSessionItem('csrf_token');
  }

  /**
   * Check storage availability
   */
  isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get storage usage info
   */
  getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0;
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // Estimate available space (5MB typical limit)
      const available = 5 * 1024 * 1024; // 5MB in bytes
      const percentage = (used / available) * 100;

      return { used, available, percentage };
    } catch {
      return { used: 0, available: 0, percentage: 0 };
    }
  }
}

// Export singleton instance
export const secureStorage = new SecureStorage();
export default secureStorage;
