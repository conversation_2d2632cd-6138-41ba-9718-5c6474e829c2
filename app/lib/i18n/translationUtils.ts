/**
 * Translation Utilities and Hooks
 * Advanced translation utilities with context and pluralization
 */

import { useTranslation, UseTranslationOptions } from 'react-i18next';
import { useState, useEffect, useCallback } from 'react';
import { i18nManager } from './i18nSetup';
import { APP_CONFIG } from '../../config/app';

interface TranslationOptions {
  count?: number;
  context?: string;
  defaultValue?: string;
  fallbackKey?: string;
  interpolation?: Record<string, any>;
  lng?: string;
  ns?: string | string[];
}

interface UseAdvancedTranslationReturn {
  t: (key: string, options?: TranslationOptions) => string;
  i18n: any;
  ready: boolean;
  language: string;
  changeLanguage: (lng: string) => Promise<void>;
  exists: (key: string, options?: any) => boolean;
  getFixedT: (lng?: string, ns?: string | string[]) => (key: string, options?: any) => string;
}

/**
 * Advanced translation hook with additional features
 */
export function useAdvancedTranslation(
  ns?: string | string[],
  options?: UseTranslationOptions
): UseAdvancedTranslationReturn {
  const { t: originalT, i18n, ready } = useTranslation(ns, options);
  const [language, setLanguage] = useState(i18n.language);

  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setLanguage(lng);
    };

    i18n.on('languageChanged', handleLanguageChange);
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  const t = useCallback((key: string, options: TranslationOptions = {}) => {
    const {
      count,
      context,
      defaultValue,
      fallbackKey,
      interpolation,
      lng,
      ns: namespace,
    } = options;

    // Build i18next options
    const i18nextOptions: any = {
      ...interpolation,
      count,
      context,
      defaultValue,
      lng,
      ns: namespace,
    };

    // Try main key first
    let result = originalT(key, i18nextOptions);
    
    // If translation not found and fallback key provided
    if (result === key && fallbackKey) {
      result = originalT(fallbackKey, i18nextOptions);
    }
    
    // If still not found, try without context
    if (result === key && context) {
      result = originalT(key, { ...i18nextOptions, context: undefined });
    }

    return result;
  }, [originalT]);

  const changeLanguage = useCallback(async (lng: string) => {
    await i18nManager.changeLanguage(lng);
  }, []);

  const exists = useCallback((key: string, options: any = {}) => {
    return i18n.exists(key, options);
  }, [i18n]);

  const getFixedT = useCallback((lng?: string, ns?: string | string[]) => {
    return i18n.getFixedT(lng, ns);
  }, [i18n]);

  return {
    t,
    i18n,
    ready,
    language,
    changeLanguage,
    exists,
    getFixedT,
  };
}

/**
 * Hook for lazy loading translation namespaces
 */
export function useLazyTranslation(namespace: string) {
  const [loaded, setLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadNamespace = useCallback(async () => {
    if (loaded || loading) return;

    setLoading(true);
    setError(null);

    try {
      await i18nManager.loadNamespace(namespace);
      setLoaded(true);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load namespace'));
    } finally {
      setLoading(false);
    }
  }, [namespace, loaded, loading]);

  useEffect(() => {
    loadNamespace();
  }, [loadNamespace]);

  return { loaded, loading, error, reload: loadNamespace };
}

/**
 * Hook for managing user language preferences
 */
export function useLanguagePreference() {
  const [currentLanguage, setCurrentLanguage] = useState(i18nManager.getCurrentLanguage());
  const [availableLanguages] = useState(APP_CONFIG.i18n.supportedLanguages);

  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      setCurrentLanguage(event.detail.language);
    };

    window.addEventListener('languageChanged', handleLanguageChange as EventListener);
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, []);

  const changeLanguage = useCallback(async (language: string) => {
    if (!i18nManager.isLanguageSupported(language)) {
      throw new Error(`Language ${language} is not supported`);
    }

    await i18nManager.changeLanguage(language);
  }, []);

  const getLanguageInfo = useCallback((code: string) => {
    return availableLanguages.find(lang => lang.code === code);
  }, [availableLanguages]);

  return {
    currentLanguage,
    availableLanguages,
    changeLanguage,
    getLanguageInfo,
    isSupported: i18nManager.isLanguageSupported,
  };
}

/**
 * Hook for formatting dates, numbers, and currencies
 */
export function useLocaleFormatting() {
  const { language } = useLanguagePreference();

  const formatDate = useCallback((
    date: Date | string | number,
    options?: Intl.DateTimeFormatOptions
  ) => {
    return i18nManager.formatDate(date, options);
  }, []);

  const formatNumber = useCallback((
    number: number,
    options?: Intl.NumberFormatOptions
  ) => {
    return i18nManager.formatNumber(number, options);
  }, []);

  const formatCurrency = useCallback((
    amount: number,
    currency = 'USD'
  ) => {
    return i18nManager.formatCurrency(amount, currency);
  }, []);

  const formatRelativeTime = useCallback((
    date: Date | string | number,
    options?: Intl.RelativeTimeFormatOptions
  ) => {
    const now = new Date();
    const target = new Date(date);
    const diffInSeconds = Math.floor((target.getTime() - now.getTime()) / 1000);

    const rtf = new Intl.RelativeTimeFormat(language, {
      numeric: 'auto',
      ...options,
    });

    // Determine the appropriate unit
    const units: Array<[string, number]> = [
      ['year', 365 * 24 * 60 * 60],
      ['month', 30 * 24 * 60 * 60],
      ['day', 24 * 60 * 60],
      ['hour', 60 * 60],
      ['minute', 60],
      ['second', 1],
    ];

    for (const [unit, secondsInUnit] of units) {
      if (Math.abs(diffInSeconds) >= secondsInUnit) {
        const value = Math.floor(diffInSeconds / secondsInUnit);
        return rtf.format(value, unit as Intl.RelativeTimeFormatUnit);
      }
    }

    return rtf.format(0, 'second');
  }, [language]);

  return {
    formatDate,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
    locale: language,
  };
}

/**
 * Translation validation utilities
 */
export const translationValidation = {
  /**
   * Check if all required translations exist
   */
  validateTranslations: (
    keys: string[],
    languages: string[] = APP_CONFIG.i18n.supportedLanguages.map(l => l.code)
  ): Record<string, string[]> => {
    const missing: Record<string, string[]> = {};

    languages.forEach(lang => {
      const missingKeys = keys.filter(key => {
        const fixedT = i18nManager.getInstance().getFixedT(lang);
        return !i18nManager.getInstance().exists(key, { lng: lang });
      });

      if (missingKeys.length > 0) {
        missing[lang] = missingKeys;
      }
    });

    return missing;
  },

  /**
   * Get translation completion percentage
   */
  getCompletionPercentage: (
    keys: string[],
    language: string
  ): number => {
    const existingKeys = keys.filter(key => 
      i18nManager.getInstance().exists(key, { lng: language })
    );
    
    return Math.round((existingKeys.length / keys.length) * 100);
  },

  /**
   * Find unused translation keys
   */
  findUnusedKeys: (
    allKeys: string[],
    usedKeys: string[]
  ): string[] => {
    return allKeys.filter(key => !usedKeys.includes(key));
  },
};

/**
 * Translation key extraction utilities
 */
export const translationExtraction = {
  /**
   * Extract translation keys from text
   */
  extractKeys: (text: string): string[] => {
    const keyPattern = /(?:t|i18n\.t)\s*\(\s*['"`]([^'"`]+)['"`]/g;
    const keys: string[] = [];
    let match;

    while ((match = keyPattern.exec(text)) !== null) {
      keys.push(match[1]);
    }

    return [...new Set(keys)]; // Remove duplicates
  },

  /**
   * Extract keys from JSX
   */
  extractJSXKeys: (jsx: string): string[] => {
    const patterns = [
      /\{t\(['"`]([^'"`]+)['"`]\)/g,
      /<Trans[^>]*i18nKey=['"`]([^'"`]+)['"`]/g,
      /i18nKey=['"`]([^'"`]+)['"`]/g,
    ];

    const keys: string[] = [];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(jsx)) !== null) {
        keys.push(match[1]);
      }
    });

    return [...new Set(keys)];
  },
};

/**
 * Pluralization utilities
 */
export const pluralizationUtils = {
  /**
   * Get plural form for a language
   */
  getPluralForm: (count: number, language: string): string => {
    const pr = new Intl.PluralRules(language);
    return pr.select(count);
  },

  /**
   * Create pluralization key
   */
  createPluralKey: (baseKey: string, form: string): string => {
    return `${baseKey}_${form}`;
  },

  /**
   * Get all plural forms for a language
   */
  getAllPluralForms: (language: string): string[] => {
    const forms = new Set<string>();
    
    // Test with different numbers to get all forms
    const testNumbers = [0, 1, 2, 3, 5, 10, 11, 100, 101];
    const pr = new Intl.PluralRules(language);
    
    testNumbers.forEach(num => {
      forms.add(pr.select(num));
    });
    
    return Array.from(forms);
  },
};

/**
 * Context-aware translation utilities
 */
export const contextUtils = {
  /**
   * Create context-aware translation key
   */
  createContextKey: (baseKey: string, context: string): string => {
    return `${baseKey}_${context}`;
  },

  /**
   * Get translation with context fallback
   */
  getWithContextFallback: (
    key: string,
    context: string,
    t: (key: string, options?: any) => string
  ): string => {
    const contextKey = contextUtils.createContextKey(key, context);
    const result = t(contextKey);
    
    // If context-specific translation doesn't exist, fall back to base key
    return result === contextKey ? t(key) : result;
  },
};

export default {
  useAdvancedTranslation,
  useLazyTranslation,
  useLanguagePreference,
  useLocaleFormatting,
  translationValidation,
  translationExtraction,
  pluralizationUtils,
  contextUtils,
};
