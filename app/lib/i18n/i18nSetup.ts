/**
 * Internationalization Setup
 * Comprehensive i18n configuration with dynamic loading and caching
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { APP_CONFIG } from '../../config/app';
import { secureStorage } from '../../utils/secureStorage';
import { loggerHelpers } from '../../store/middleware/loggingMiddleware';

interface TranslationResource {
  [key: string]: {
    [namespace: string]: {
      [key: string]: string | object;
    };
  };
}

interface I18nConfig {
  debug?: boolean;
  fallbackLng: string;
  defaultNS: string;
  supportedLngs: string[];
  detection: {
    order: string[];
    caches: string[];
    lookupLocalStorage: string;
    lookupSessionStorage: string;
  };
  backend: {
    loadPath: string;
    addPath: string;
    allowMultiLoading: boolean;
    crossDomain: boolean;
    withCredentials: boolean;
    requestOptions: RequestInit;
  };
  interpolation: {
    escapeValue: boolean;
    formatSeparator: string;
    format: (value: any, format: string, lng: string) => string;
  };
  react: {
    useSuspense: boolean;
    bindI18n: string;
    bindI18nStore: string;
    transEmptyNodeValue: string;
    transSupportBasicHtmlNodes: boolean;
    transKeepBasicHtmlNodesFor: string[];
  };
}

class I18nManager {
  private initialized = false;
  private loadedResources: Set<string> = new Set();
  private fallbackResources: TranslationResource = {};

  constructor() {
    this.setupFallbackResources();
  }

  /**
   * Initialize i18n system
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    const config = this.createConfig();

    try {
      await i18n
        .use(Backend)
        .use(LanguageDetector)
        .use(initReactI18next)
        .init(config);

      this.initialized = true;
      this.setupEventListeners();
      
      loggerHelpers.info('i18n initialized', {
        language: i18n.language,
        supportedLanguages: config.supportedLngs,
      });
    } catch (error) {
      loggerHelpers.error('i18n initialization failed', error);
      this.initializeFallback();
    }
  }

  /**
   * Create i18n configuration
   */
  private createConfig(): I18nConfig {
    return {
      debug: import.meta.env.NODE_ENV === 'development',
      fallbackLng: APP_CONFIG.i18n.fallbackLanguage,
      defaultNS: APP_CONFIG.i18n.namespaces[0],
      supportedLngs: APP_CONFIG.i18n.supportedLanguages.map(lang => lang.code),
      
      detection: {
        order: [
          'localStorage',
          'sessionStorage',
          'navigator',
          'htmlTag',
          'path',
          'subdomain',
        ],
        caches: ['localStorage', 'sessionStorage'],
        lookupLocalStorage: 'preferred_language',
        lookupSessionStorage: 'session_language',
      },

      backend: {
        loadPath: APP_CONFIG.i18n.loadPath,
        addPath: '/api/translations/add/{{lng}}/{{ns}}',
        allowMultiLoading: true,
        crossDomain: false,
        withCredentials: true,
        requestOptions: {
          cache: 'default',
          credentials: 'same-origin',
          mode: 'cors',
        },
      },

      interpolation: {
        escapeValue: false, // React already escapes
        formatSeparator: ',',
        format: this.formatValue.bind(this),
      },

      react: {
        useSuspense: false,
        bindI18n: 'languageChanged loaded',
        bindI18nStore: 'added removed',
        transEmptyNodeValue: '',
        transSupportBasicHtmlNodes: true,
        transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'b', 'u'],
      },
    };
  }

  /**
   * Format interpolated values
   */
  private formatValue(value: any, format: string, lng: string): string {
    if (format === 'uppercase') return value.toUpperCase();
    if (format === 'lowercase') return value.toLowerCase();
    if (format === 'capitalize') return value.charAt(0).toUpperCase() + value.slice(1);
    
    // Date formatting
    if (format.startsWith('date')) {
      const dateFormat = format.split(':')[1] || 'short';
      return new Intl.DateTimeFormat(lng, {
        dateStyle: dateFormat as any,
      }).format(new Date(value));
    }
    
    // Number formatting
    if (format.startsWith('number')) {
      const numberFormat = format.split(':')[1] || 'decimal';
      return new Intl.NumberFormat(lng, {
        style: numberFormat,
      }).format(value);
    }
    
    // Currency formatting
    if (format.startsWith('currency')) {
      const currency = format.split(':')[1] || 'USD';
      return new Intl.NumberFormat(lng, {
        style: 'currency',
        currency,
      }).format(value);
    }
    
    return value;
  }

  /**
   * Setup fallback resources for offline use
   */
  private setupFallbackResources(): void {
    this.fallbackResources = {
      en: {
        common: {
          welcome: 'Welcome',
          save: 'Save',
          cancel: 'Cancel',
          delete: 'Delete',
          edit: 'Edit',
          create: 'Create',
          update: 'Update',
          loading: 'Loading...',
          error: 'An error occurred',
          success: 'Operation completed successfully',
          confirm: 'Are you sure?',
          yes: 'Yes',
          no: 'No',
          ok: 'OK',
          close: 'Close',
          search: 'Search',
          filter: 'Filter',
          export: 'Export',
          import: 'Import',
          refresh: 'Refresh',
          settings: 'Settings',
          profile: 'Profile',
          logout: 'Logout',
          login: 'Login',
          register: 'Register',
          dashboard: 'Dashboard',
          users: 'Users',
          roles: 'Roles',
          permissions: 'Permissions',
          translations: 'Translations',
          audit: 'Audit Logs',
          security: 'Security',
          system: 'System',
          administration: 'Administration',
        },
        auth: {
          loginTitle: 'Sign In',
          loginSubtitle: 'Welcome back! Please sign in to your account.',
          email: 'Email',
          password: 'Password',
          rememberMe: 'Remember me',
          forgotPassword: 'Forgot password?',
          signIn: 'Sign In',
          signUp: 'Sign Up',
          createAccount: 'Create Account',
          alreadyHaveAccount: 'Already have an account?',
          dontHaveAccount: "Don't have an account?",
          invalidCredentials: 'Invalid email or password',
          accountLocked: 'Account is temporarily locked',
          sessionExpired: 'Your session has expired',
          logoutSuccess: 'You have been logged out successfully',
        },
        dashboard: {
          title: 'Dashboard',
          welcomeBack: 'Welcome back, {{name}}!',
          overview: "Here's what's happening with your admin panel today.",
          totalUsers: 'Total Users',
          activeUsers: 'Active Users',
          revenue: 'Revenue',
          conversionRate: 'Conversion Rate',
          recentActivity: 'Recent Activity',
          systemHealth: 'System Health',
          quickActions: 'Quick Actions',
          addNewUser: 'Add New User',
          createContent: 'Create Content',
          viewReports: 'View Reports',
          systemSettings: 'System Settings',
        },
        errors: {
          notFound: 'Page not found',
          unauthorized: 'You are not authorized to access this page',
          forbidden: 'Access denied',
          serverError: 'Internal server error',
          networkError: 'Network connection error',
          validationError: 'Please check your input',
          unknownError: 'An unknown error occurred',
        },
      },
    };
  }

  /**
   * Initialize fallback mode
   */
  private initializeFallback(): void {
    // Simple fallback implementation
    const fallbackT = (key: string, options: any = {}) => {
      const keys = key.split('.');
      let value: any = this.fallbackResources.en;
      
      for (const k of keys) {
        value = value?.[k];
        if (!value) break;
      }
      
      if (typeof value === 'string' && options) {
        return this.interpolateString(value, options);
      }
      
      return value || key;
    };

    // Mock i18n object
    (window as any).i18n = {
      t: fallbackT,
      language: 'en',
      changeLanguage: () => Promise.resolve(),
      exists: () => false,
      getFixedT: () => fallbackT,
    };

    this.initialized = true;
    loggerHelpers.warn('i18n initialized in fallback mode');
  }

  /**
   * Simple string interpolation for fallback
   */
  private interpolateString(str: string, options: Record<string, any>): string {
    return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return options[key] || match;
    });
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    i18n.on('languageChanged', (lng) => {
      loggerHelpers.info('Language changed', { language: lng });
      
      // Update document language
      document.documentElement.lang = lng;
      
      // Update direction for RTL languages
      const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
      document.documentElement.dir = rtlLanguages.includes(lng) ? 'rtl' : 'ltr';
      
      // Store preference
      secureStorage.setItem('preferred_language', lng, false);
      
      // Emit custom event
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lng } }));
    });

    i18n.on('loaded', (loaded) => {
      loggerHelpers.debug('i18n resources loaded', loaded);
    });

    i18n.on('failedLoading', (lng, ns, msg) => {
      loggerHelpers.error('i18n resource loading failed', { lng, ns, msg });
    });
  }

  /**
   * Change language
   */
  async changeLanguage(language: string): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      await i18n.changeLanguage(language);
      loggerHelpers.info('Language changed successfully', { language });
    } catch (error) {
      loggerHelpers.error('Failed to change language', error);
      throw error;
    }
  }

  /**
   * Load namespace
   */
  async loadNamespace(namespace: string, language?: string): Promise<void> {
    const lng = language || i18n.language;
    const resourceKey = `${lng}:${namespace}`;

    if (this.loadedResources.has(resourceKey)) {
      return;
    }

    try {
      await i18n.loadNamespaces(namespace);
      this.loadedResources.add(resourceKey);
      loggerHelpers.debug('Namespace loaded', { namespace, language: lng });
    } catch (error) {
      loggerHelpers.error('Failed to load namespace', { namespace, language: lng, error });
    }
  }

  /**
   * Add translation resources
   */
  addResources(language: string, namespace: string, resources: object): void {
    i18n.addResourceBundle(language, namespace, resources, true, true);
    loggerHelpers.debug('Resources added', { language, namespace });
  }

  /**
   * Get current language
   */
  getCurrentLanguage(): string {
    return i18n.language || APP_CONFIG.i18n.defaultLanguage;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): typeof APP_CONFIG.i18n.supportedLanguages {
    return APP_CONFIG.i18n.supportedLanguages;
  }

  /**
   * Check if language is supported
   */
  isLanguageSupported(language: string): boolean {
    return APP_CONFIG.i18n.supportedLanguages.some(lang => lang.code === language);
  }

  /**
   * Get translation with fallback
   */
  translate(key: string, options?: any): string {
    if (!this.initialized) {
      return key;
    }

    try {
      return i18n.t(key, options);
    } catch (error) {
      loggerHelpers.error('Translation failed', { key, error });
      return key;
    }
  }

  /**
   * Check if translation exists
   */
  exists(key: string, options?: any): boolean {
    return i18n.exists(key, options);
  }

  /**
   * Get language direction
   */
  getDirection(language?: string): 'ltr' | 'rtl' {
    const lng = language || this.getCurrentLanguage();
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.includes(lng) ? 'rtl' : 'ltr';
  }

  /**
   * Format date according to current locale
   */
  formatDate(date: Date | string | number, options?: Intl.DateTimeFormatOptions): string {
    const dateObj = new Date(date);
    const locale = this.getCurrentLanguage();
    
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...options,
    }).format(dateObj);
  }

  /**
   * Format number according to current locale
   */
  formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
    const locale = this.getCurrentLanguage();
    return new Intl.NumberFormat(locale, options).format(number);
  }

  /**
   * Format currency according to current locale
   */
  formatCurrency(amount: number, currency = 'USD'): string {
    const locale = this.getCurrentLanguage();
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  }

  /**
   * Get initialization status
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get i18n instance
   */
  getInstance() {
    return i18n;
  }
}

// Export singleton instance
export const i18nManager = new I18nManager();

// Auto-initialize
if (typeof window !== 'undefined') {
  i18nManager.initialize().catch(error => {
    console.error('Failed to initialize i18n:', error);
  });
}

export default i18nManager;
