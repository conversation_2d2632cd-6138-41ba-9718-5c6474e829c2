import { redirect } from "react-router";

import type { User } from "./types";
import { rbacService } from "./service";

export interface GuardOptions {
  resource: string;
  action: string;
  redirectTo?: string;
  context?: Record<string, any>;
}

export async function requireAuth(request: Request): Promise<User> {
  const user = await rbacService.getCurrentUser();
  if (!user) {
    throw redirect("/login");
  }
  return user;
}

export async function requirePermission(
  user: User,
  options: GuardOptions
): Promise<void> {
  const hasPermission = await rbacService.checkPermission(
    user.id,
    options.resource,
    options.action,
    options.context
  );

  if (!hasPermission) {
    throw redirect(options.redirectTo || "/unauthorized");
  }
}

export async function requireRole(
  user: User,
  roleNames: string[]
): Promise<void> {
  const userRoles = user.roles.map((r) => r.roleId);
  const hasRole = roleNames.some((role) => userRoles.includes(role));

  if (!hasRole) {
    throw redirect("/unauthorized");
  }
}
