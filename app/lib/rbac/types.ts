/**
 * Core RBAC Types for Multi-tenant Role-Based Access Control System
 */

// Base types
export type UUID = string;
export type Timestamp = string; // ISO 8601 format

// Permission granularity levels
export type PermissionLevel = 'feature' | 'resource' | 'action';

// Permission actions
export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'execute' | 'manage';

// Tenant/Organization context
export interface Tenant {
  id: UUID;
  name: string;
  slug: string;
  domain?: string;
  settings: TenantSettings;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TenantSettings {
  allowedRoles: string[];
  customPermissions: Record<string, Permission>;
  roleHierarchy: RoleHierarchy;
  features: TenantFeature[];
}

export interface TenantFeature {
  name: string;
  enabled: boolean;
  config?: Record<string, any>;
}

// Permission system
export interface Permission {
  id: UUID;
  name: string;
  description: string;
  level: PermissionLevel;
  resource: string;
  actions: PermissionAction[];
  conditions?: PermissionCondition[];
  metadata?: Record<string, any>;
}

export interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
  context?: 'user' | 'tenant' | 'resource' | 'request';
}

// Role system with hierarchy support
export interface Role {
  id: UUID;
  name: string;
  displayName: string;
  description: string;
  level: number; // For hierarchy (0 = highest, higher numbers = lower levels)
  permissions: Permission[];
  inheritsFrom?: UUID[]; // Parent roles for inheritance
  tenantId?: UUID; // Optional tenant-specific role
  isSystemRole: boolean; // System roles vs custom roles
  metadata?: Record<string, any>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface RoleHierarchy {
  [roleName: string]: {
    level: number;
    inherits: string[];
    permissions: string[];
  };
}

// User system
export interface User {
  id: UUID;
  email: string;
  username?: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// User-Tenant-Role association (many-to-many with context)
export interface UserTenantRole {
  id: UUID;
  userId: UUID;
  tenantId: UUID;
  roleId: UUID;
  isActive: boolean;
  assignedBy: UUID;
  assignedAt: Timestamp;
  expiresAt?: Timestamp;
  metadata?: Record<string, any>;
}

// Complete user context with resolved permissions
export interface UserContext {
  user: User;
  currentTenant: Tenant;
  tenantRoles: UserTenantRole[];
  resolvedPermissions: ResolvedPermission[];
  effectiveRole: Role; // Highest priority role for current tenant
  sessionId: string;
  isAuthenticated: boolean;
}

export interface ResolvedPermission extends Permission {
  source: 'role' | 'direct' | 'inherited';
  sourceId: UUID;
  tenantId: UUID;
  effectiveActions: PermissionAction[];
}

// Authorization context and results
export interface AuthorizationContext {
  user: UserContext;
  resource?: string;
  action?: PermissionAction;
  resourceId?: UUID;
  additionalContext?: Record<string, any>;
}

export interface AuthorizationResult {
  allowed: boolean;
  reason?: string;
  requiredPermissions?: string[];
  missingPermissions?: string[];
  conditions?: PermissionCondition[];
  metadata?: Record<string, any>;
}

// Route protection types
export interface RoutePermission {
  resource: string;
  actions: PermissionAction[];
  conditions?: PermissionCondition[];
  requireAll?: boolean; // true = AND logic, false = OR logic
}

export interface ProtectedRouteConfig {
  path: string;
  permissions: RoutePermission[];
  roles?: string[]; // Alternative to permissions - check by role names
  tenantRequired?: boolean;
  redirectOnUnauthorized?: string;
  fallbackComponent?: React.ComponentType;
}

// API and service types
export interface AuthenticationRequest {
  email: string;
  password: string;
  tenantSlug?: string;
}

export interface AuthenticationResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Timestamp;
  tenants: Tenant[];
  defaultTenant?: Tenant;
}

export interface RoleAssignmentRequest {
  userId: UUID;
  tenantId: UUID;
  roleId: UUID;
  expiresAt?: Timestamp;
  metadata?: Record<string, any>;
}

// Error types
export interface RBACError {
  code: string;
  message: string;
  details?: Record<string, any>;
  statusCode?: number;
}

export type RBACErrorCode = 
  | 'UNAUTHORIZED'
  | 'FORBIDDEN' 
  | 'INVALID_TOKEN'
  | 'TOKEN_EXPIRED'
  | 'TENANT_NOT_FOUND'
  | 'ROLE_NOT_FOUND'
  | 'PERMISSION_DENIED'
  | 'INVALID_CREDENTIALS'
  | 'USER_INACTIVE'
  | 'TENANT_INACTIVE';

// Configuration types
export interface RBACConfig {
  tokenExpiry: number; // in seconds
  refreshTokenExpiry: number; // in seconds
  defaultRole: string;
  systemRoles: string[];
  enableRoleHierarchy: boolean;
  enablePermissionInheritance: boolean;
  enableTenantIsolation: boolean;
  cachePermissions: boolean;
  cacheTTL: number; // in seconds
}

// React component prop types
export interface WithRBACProps {
  userContext?: UserContext;
  loading?: boolean;
  error?: RBACError;
}

export interface PermissionGuardProps {
  permissions?: RoutePermission[];
  roles?: string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean;
}

export interface RoleGuardProps {
  roles: string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean;
}

// Utility types
export type PermissionCheck = (
  context: AuthorizationContext
) => Promise<AuthorizationResult>;

export type RoleResolver = (
  userId: UUID,
  tenantId: UUID
) => Promise<Role[]>;

export type PermissionResolver = (
  roles: Role[],
  tenantId: UUID
) => Promise<ResolvedPermission[]>;
