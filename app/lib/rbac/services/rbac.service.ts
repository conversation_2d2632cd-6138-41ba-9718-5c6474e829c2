/**
 * Main RBAC Service
 * Orchestrates authentication, authorization, and user context management
 */

import type {
  UserContext,
  AuthenticationRequest,
  AuthenticationResponse,
  AuthorizationContext,
  AuthorizationResult,
  PermissionAction,
  UUID,
  RBACError,
} from '../types';
import { AuthService } from './auth.service';
import { RoleService } from './role.service';
import { PermissionService } from './permission.service';
import { RBAC_ERROR_MESSAGES } from '../constants';
import { getEffectiveRole } from '../utils';

export class RBACService {
  private authService: AuthService;
  private roleService: RoleService;
  private permissionService: PermissionService;

  constructor() {
    this.authService = new AuthService();
    this.roleService = new RoleService();
    this.permissionService = new PermissionService();
  }

  /**
   * Authenticate user and return authentication response
   */
  async authenticate(request: AuthenticationRequest): Promise<AuthenticationResponse> {
    return this.authService.authenticate(request);
  }

  /**
   * Validate token and build complete user context
   */
  async validateTokenAndBuildContext(token: string): Promise<UserContext | null> {
    try {
      // Validate token and get basic user context
      const basicContext = await this.authService.validateToken(token);
      if (!basicContext) {
        return null;
      }

      // Get user's roles for current tenant
      const userTenantRoles = await this.roleService.getUserRoles(basicContext.user.id);
      const tenantRoles = userTenantRoles.filter(
        utr => utr.tenantId === basicContext.currentTenant.id && utr.isActive
      );

      if (tenantRoles.length === 0) {
        return null; // User has no roles in current tenant
      }

      // Get role objects
      const roleIds = tenantRoles.map(utr => utr.roleId);
      const roles = await Promise.all(
        roleIds.map(roleId => this.roleService.getRoleById(roleId))
      );
      const validRoles = roles.filter(role => role !== null) as any[];

      // Get effective role (highest priority)
      const effectiveRole = getEffectiveRole(validRoles, this.roleService.getRoleHierarchy());

      // Resolve permissions
      const resolvedPermissions = await this.permissionService.resolveUserPermissions(
        validRoles,
        basicContext.currentTenant.id
      );

      // Build complete user context
      const userContext: UserContext = {
        ...basicContext,
        tenantRoles,
        resolvedPermissions,
        effectiveRole: effectiveRole!,
      };

      return userContext;
    } catch (error) {
      console.error('Error building user context:', error);
      return null;
    }
  }

  /**
   * Check if user has permission for a specific action
   */
  async checkPermission(
    userContext: UserContext,
    resource: string,
    action: PermissionAction,
    resourceId?: UUID,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    return this.permissionService.checkPermission(
      userContext,
      resource,
      action,
      resourceId,
      additionalContext
    );
  }

  /**
   * Check multiple permissions with AND logic
   */
  async checkAllPermissions(
    userContext: UserContext,
    permissions: Array<{
      resource: string;
      action: PermissionAction;
      resourceId?: UUID;
    }>,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    return this.permissionService.checkAllPermissions(
      userContext,
      permissions,
      additionalContext
    );
  }

  /**
   * Check multiple permissions with OR logic
   */
  async checkAnyPermission(
    userContext: UserContext,
    permissions: Array<{
      resource: string;
      action: PermissionAction;
      resourceId?: UUID;
    }>,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    return this.permissionService.checkAnyPermission(
      userContext,
      permissions,
      additionalContext
    );
  }

  /**
   * Check if user has specific role
   */
  async checkRole(
    userContext: UserContext,
    roleName: string
  ): Promise<boolean> {
    return this.roleService.userHasRole(
      userContext.user.id,
      userContext.currentTenant.id,
      roleName
    );
  }

  /**
   * Check if user has any of the specified roles
   */
  async checkAnyRole(
    userContext: UserContext,
    roleNames: string[]
  ): Promise<boolean> {
    return this.roleService.userHasAnyRole(
      userContext.user.id,
      userContext.currentTenant.id,
      roleNames
    );
  }

  /**
   * Check if user has all of the specified roles
   */
  async checkAllRoles(
    userContext: UserContext,
    roleNames: string[]
  ): Promise<boolean> {
    return this.roleService.userHasAllRoles(
      userContext.user.id,
      userContext.currentTenant.id,
      roleNames
    );
  }

  /**
   * Logout user
   */
  async logout(token: string): Promise<void> {
    return this.authService.logout(token);
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<{ token: string; expiresAt: string }> {
    return this.authService.refreshToken(refreshToken);
  }

  /**
   * Get user's available actions for a resource
   */
  getAvailableActions(userContext: UserContext, resource: string): PermissionAction[] {
    return this.permissionService.getAvailableActions(userContext, resource);
  }

  /**
   * Check if user can access a resource
   */
  canAccessResource(userContext: UserContext, resource: string): boolean {
    return this.permissionService.canAccessResource(userContext, resource);
  }

  /**
   * Get permission summary for user
   */
  getPermissionSummary(userContext: UserContext) {
    return this.permissionService.getPermissionSummary(userContext);
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(
    userId: UUID,
    tenantId: UUID,
    roleId: UUID,
    assignedBy: UUID,
    expiresAt?: string
  ) {
    return this.roleService.assignRoleToUser(userId, tenantId, roleId, assignedBy, expiresAt);
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(userId: UUID, tenantId: UUID, roleId: UUID) {
    return this.roleService.removeRoleFromUser(userId, tenantId, roleId);
  }

  /**
   * Get all available roles
   */
  async getAllRoles() {
    return this.roleService.getAllRoles();
  }

  /**
   * Get system roles only
   */
  async getSystemRoles() {
    return this.roleService.getSystemRoles();
  }

  /**
   * Create authorization context for additional checks
   */
  createAuthorizationContext(
    userContext: UserContext,
    resource?: string,
    action?: PermissionAction,
    resourceId?: UUID,
    additionalContext?: Record<string, any>
  ): AuthorizationContext {
    return {
      user: userContext,
      resource,
      action,
      resourceId,
      additionalContext,
    };
  }

  /**
   * Validate user context is still valid
   */
  async validateUserContext(userContext: UserContext): Promise<boolean> {
    try {
      // Check if user is still active
      if (!userContext.user.isActive || !userContext.isAuthenticated) {
        return false;
      }

      // Check if tenant roles are still active
      const currentRoles = await this.roleService.getUserRoles(userContext.user.id);
      const activeTenantRoles = currentRoles.filter(
        utr => utr.tenantId === userContext.currentTenant.id && utr.isActive
      );

      return activeTenantRoles.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get role hierarchy
   */
  getRoleHierarchy() {
    return this.roleService.getRoleHierarchy();
  }

  /**
   * Create RBAC error
   */
  createError(code: string, message: string, statusCode?: number): RBACError {
    return {
      code,
      message,
      statusCode: statusCode || 500,
    };
  }
}

// Export singleton instance
export const rbacService = new RBACService();
