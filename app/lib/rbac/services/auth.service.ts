/**
 * Authentication Service
 * Handles user authentication, token management, and session handling
 */

import type {
  User,
  Tenant,
  UserContext,
  AuthenticationRequest,
  AuthenticationResponse,
  RBACError,
  UUID,
} from '../types';
import { RBAC_ERROR_MESSAGES, HTTP_STATUS } from '../constants';

// Mock data store - In production, this would connect to your database
class MockDataStore {
  private users: Map<string, User> = new Map();
  private tenants: Map<string, Tenant> = new Map();
  private userPasswords: Map<string, string> = new Map();
  private sessions: Map<string, { userId: string; tenantId: string; expiresAt: Date }> = new Map();

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    // Create mock tenants
    const tenant1: Tenant = {
      id: 'tenant-1',
      name: 'Acme Corporation',
      slug: 'acme',
      domain: 'acme.com',
      settings: {
        allowedRoles: ['admin', 'editor', 'viewer'],
        customPermissions: {},
        roleHierarchy: {},
        features: [
          { name: 'dashboard', enabled: true },
          { name: 'reports', enabled: true },
          { name: 'user_management', enabled: true },
        ],
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const tenant2: Tenant = {
      id: 'tenant-2',
      name: 'Beta Industries',
      slug: 'beta',
      domain: 'beta.com',
      settings: {
        allowedRoles: ['admin', 'viewer'],
        customPermissions: {},
        roleHierarchy: {},
        features: [
          { name: 'dashboard', enabled: true },
          { name: 'reports', enabled: false },
          { name: 'user_management', enabled: false },
        ],
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.tenants.set(tenant1.id, tenant1);
    this.tenants.set(tenant2.id, tenant2);

    // Create mock users
    const user1: User = {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Admin',
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const user2: User = {
      id: 'user-2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Editor',
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const user3: User = {
      id: 'user-3',
      email: '<EMAIL>',
      firstName: 'Bob',
      lastName: 'Viewer',
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.users.set(user1.id, user1);
    this.users.set(user2.id, user2);
    this.users.set(user3.id, user3);

    // Set passwords (in production, these would be hashed)
    this.userPasswords.set(user1.email, 'admin123');
    this.userPasswords.set(user2.email, 'editor123');
    this.userPasswords.set(user3.email, 'viewer123');
  }

  async getUserByEmail(email: string): Promise<User | null> {
    for (const user of this.users.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return null;
  }

  async getUserById(id: string): Promise<User | null> {
    return this.users.get(id) || null;
  }

  async getTenantBySlug(slug: string): Promise<Tenant | null> {
    for (const tenant of this.tenants.values()) {
      if (tenant.slug === slug) {
        return tenant;
      }
    }
    return null;
  }

  async getTenantById(id: string): Promise<Tenant | null> {
    return this.tenants.get(id) || null;
  }

  async validatePassword(email: string, password: string): Promise<boolean> {
    const storedPassword = this.userPasswords.get(email);
    return storedPassword === password; // In production, use proper password hashing
  }

  async getUserTenants(userId: string): Promise<Tenant[]> {
    // Mock: return all tenants for simplicity
    // In production, this would query user-tenant relationships
    return Array.from(this.tenants.values());
  }

  async createSession(userId: string, tenantId: string): Promise<string> {
    const sessionId = `session-${Date.now()}-${Math.random()}`;
    const expiresAt = new Date(Date.now() + 3600000); // 1 hour
    
    this.sessions.set(sessionId, { userId, tenantId, expiresAt });
    return sessionId;
  }

  async getSession(sessionId: string): Promise<{ userId: string; tenantId: string } | null> {
    const session = this.sessions.get(sessionId);
    if (!session || session.expiresAt < new Date()) {
      this.sessions.delete(sessionId);
      return null;
    }
    return { userId: session.userId, tenantId: session.tenantId };
  }

  async deleteSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
  }
}

export class AuthService {
  private dataStore: MockDataStore;

  constructor() {
    this.dataStore = new MockDataStore();
  }

  /**
   * Authenticate user with email and password
   */
  async authenticate(request: AuthenticationRequest): Promise<AuthenticationResponse> {
    try {
      // Validate user credentials
      const user = await this.dataStore.getUserByEmail(request.email);
      if (!user) {
        throw this.createError('INVALID_CREDENTIALS', RBAC_ERROR_MESSAGES.INVALID_CREDENTIALS);
      }

      if (!user.isActive) {
        throw this.createError('USER_INACTIVE', RBAC_ERROR_MESSAGES.USER_INACTIVE);
      }

      const isValidPassword = await this.dataStore.validatePassword(request.email, request.password);
      if (!isValidPassword) {
        throw this.createError('INVALID_CREDENTIALS', RBAC_ERROR_MESSAGES.INVALID_CREDENTIALS);
      }

      // Get user's tenants
      const tenants = await this.dataStore.getUserTenants(user.id);
      if (tenants.length === 0) {
        throw this.createError('TENANT_NOT_FOUND', RBAC_ERROR_MESSAGES.TENANT_NOT_FOUND);
      }

      // Determine default tenant
      let defaultTenant = tenants[0];
      if (request.tenantSlug) {
        const requestedTenant = tenants.find(t => t.slug === request.tenantSlug);
        if (requestedTenant) {
          defaultTenant = requestedTenant;
        }
      }

      // Create session
      const sessionId = await this.dataStore.createSession(user.id, defaultTenant.id);

      // Generate tokens (mock implementation)
      const token = this.generateToken(user.id, defaultTenant.id, sessionId);
      const refreshToken = this.generateRefreshToken(user.id);

      return {
        user,
        token,
        refreshToken,
        expiresAt: new Date(Date.now() + 3600000).toISOString(), // 1 hour
        tenants,
        defaultTenant,
      };
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error;
      }
      throw this.createError('INTERNAL_ERROR', 'Authentication failed');
    }
  }

  /**
   * Validate authentication token and return user context
   */
  async validateToken(token: string): Promise<UserContext | null> {
    try {
      const payload = this.decodeToken(token);
      if (!payload) {
        return null;
      }

      const session = await this.dataStore.getSession(payload.sessionId);
      if (!session) {
        return null;
      }

      const user = await this.dataStore.getUserById(session.userId);
      const tenant = await this.dataStore.getTenantById(session.tenantId);

      if (!user || !tenant || !user.isActive) {
        return null;
      }

      // This would typically include role and permission resolution
      // For now, return basic context
      return {
        user,
        currentTenant: tenant,
        tenantRoles: [], // Will be populated by RoleService
        resolvedPermissions: [], // Will be populated by PermissionService
        effectiveRole: null as any, // Will be populated by RoleService
        sessionId: payload.sessionId,
        isAuthenticated: true,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(token: string): Promise<void> {
    try {
      const payload = this.decodeToken(token);
      if (payload) {
        await this.dataStore.deleteSession(payload.sessionId);
      }
    } catch (error) {
      // Ignore errors during logout
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<{ token: string; expiresAt: string }> {
    // Mock implementation - in production, validate refresh token properly
    const payload = this.decodeToken(refreshToken);
    if (!payload) {
      throw this.createError('INVALID_TOKEN', RBAC_ERROR_MESSAGES.INVALID_TOKEN);
    }

    const newToken = this.generateToken(payload.userId, payload.tenantId, payload.sessionId);
    return {
      token: newToken,
      expiresAt: new Date(Date.now() + 3600000).toISOString(),
    };
  }

  /**
   * Generate JWT token (mock implementation)
   */
  private generateToken(userId: string, tenantId: string, sessionId: string): string {
    const payload = {
      userId,
      tenantId,
      sessionId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };
    
    // In production, use proper JWT signing
    return `mock.${btoa(JSON.stringify(payload))}.signature`;
  }

  /**
   * Generate refresh token (mock implementation)
   */
  private generateRefreshToken(userId: string): string {
    const payload = {
      userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 604800, // 7 days
    };
    
    return `refresh.${btoa(JSON.stringify(payload))}.signature`;
  }

  /**
   * Decode JWT token (mock implementation)
   */
  private decodeToken(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;
      
      const payload = JSON.parse(atob(parts[1]));
      
      // Check expiration
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        return null;
      }
      
      return payload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Create RBAC error
   */
  private createError(code: string, message: string): RBACError {
    return {
      code,
      message,
      statusCode: HTTP_STATUS.UNAUTHORIZED,
    };
  }
}
