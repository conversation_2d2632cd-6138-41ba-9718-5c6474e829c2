/**
 * Permission Service
 * Handles permission resolution, checking, and authorization logic
 */

import type {
  Permission,
  ResolvedPermission,
  Role,
  UserContext,
  AuthorizationContext,
  AuthorizationResult,
  PermissionAction,
  UUID,
  RBACError,
} from '../types';
import { DEFAULT_ROLE_HIERARCHY, RBAC_ERROR_MESSAGES } from '../constants';
import {
  resolvePermissions,
  hasPermission,
  evaluateConditions,
  createAuthorizationResult,
  isWildcardPermission,
} from '../utils';

export class PermissionService {
  /**
   * Resolve all permissions for a user's roles in a tenant
   */
  async resolveUserPermissions(
    roles: Role[],
    tenantId: UUID
  ): Promise<ResolvedPermission[]> {
    return resolvePermissions(roles, tenantId, DEFAULT_ROLE_HIERARCHY);
  }

  /**
   * Check if user has a specific permission
   */
  async checkPermission(
    userContext: UserContext,
    resource: string,
    action: PermissionAction,
    resourceId?: UUID,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    try {
      // Check if user is authenticated and active
      if (!userContext.isAuthenticated || !userContext.user.isActive) {
        return createAuthorizationResult(
          false,
          'User is not authenticated or inactive',
          [`${resource}:${action}`]
        );
      }

      // Find matching permissions
      const matchingPermissions = this.findMatchingPermissions(
        userContext.resolvedPermissions,
        resource,
        action
      );

      if (matchingPermissions.length === 0) {
        return createAuthorizationResult(
          false,
          'No matching permissions found',
          [`${resource}:${action}`],
          [`${resource}:${action}`]
        );
      }

      // Check conditions for each matching permission
      const authContext: AuthorizationContext = {
        user: userContext,
        resource,
        action,
        resourceId,
        additionalContext,
      };

      for (const permission of matchingPermissions) {
        if (this.evaluatePermissionConditions(permission, authContext)) {
          return createAuthorizationResult(true, 'Permission granted');
        }
      }

      return createAuthorizationResult(
        false,
        'Permission conditions not met',
        [`${resource}:${action}`]
      );
    } catch (error) {
      return createAuthorizationResult(
        false,
        'Error checking permission',
        [`${resource}:${action}`]
      );
    }
  }

  /**
   * Check multiple permissions (AND logic)
   */
  async checkAllPermissions(
    userContext: UserContext,
    permissions: Array<{
      resource: string;
      action: PermissionAction;
      resourceId?: UUID;
    }>,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    const requiredPermissions: string[] = [];
    const missingPermissions: string[] = [];

    for (const perm of permissions) {
      const result = await this.checkPermission(
        userContext,
        perm.resource,
        perm.action,
        perm.resourceId,
        additionalContext
      );

      requiredPermissions.push(`${perm.resource}:${perm.action}`);

      if (!result.allowed) {
        missingPermissions.push(`${perm.resource}:${perm.action}`);
      }
    }

    const allowed = missingPermissions.length === 0;
    return createAuthorizationResult(
      allowed,
      allowed ? 'All permissions granted' : 'Some permissions missing',
      requiredPermissions,
      missingPermissions
    );
  }

  /**
   * Check multiple permissions (OR logic)
   */
  async checkAnyPermission(
    userContext: UserContext,
    permissions: Array<{
      resource: string;
      action: PermissionAction;
      resourceId?: UUID;
    }>,
    additionalContext?: Record<string, any>
  ): Promise<AuthorizationResult> {
    const requiredPermissions: string[] = [];
    const missingPermissions: string[] = [];

    for (const perm of permissions) {
      const result = await this.checkPermission(
        userContext,
        perm.resource,
        perm.action,
        perm.resourceId,
        additionalContext
      );

      requiredPermissions.push(`${perm.resource}:${perm.action}`);

      if (result.allowed) {
        return createAuthorizationResult(true, 'Permission granted');
      } else {
        missingPermissions.push(`${perm.resource}:${perm.action}`);
      }
    }

    return createAuthorizationResult(
      false,
      'No matching permissions found',
      requiredPermissions,
      missingPermissions
    );
  }

  /**
   * Get all permissions for a resource
   */
  getResourcePermissions(
    userContext: UserContext,
    resource: string
  ): ResolvedPermission[] {
    return userContext.resolvedPermissions.filter(
      permission => permission.resource === resource || permission.resource === '*'
    );
  }

  /**
   * Get all available actions for a resource
   */
  getAvailableActions(
    userContext: UserContext,
    resource: string
  ): PermissionAction[] {
    const permissions = this.getResourcePermissions(userContext, resource);
    const actions = new Set<PermissionAction>();

    permissions.forEach(permission => {
      permission.effectiveActions.forEach(action => actions.add(action));
    });

    return Array.from(actions);
  }

  /**
   * Check if user can perform any action on a resource
   */
  canAccessResource(userContext: UserContext, resource: string): boolean {
    const permissions = this.getResourcePermissions(userContext, resource);
    return permissions.length > 0;
  }

  /**
   * Get permission summary for user
   */
  getPermissionSummary(userContext: UserContext): {
    totalPermissions: number;
    resourceCount: number;
    resources: string[];
    wildcardPermissions: number;
  } {
    const resources = new Set<string>();
    let wildcardCount = 0;

    userContext.resolvedPermissions.forEach(permission => {
      resources.add(permission.resource);
      if (isWildcardPermission(permission)) {
        wildcardCount++;
      }
    });

    return {
      totalPermissions: userContext.resolvedPermissions.length,
      resourceCount: resources.size,
      resources: Array.from(resources),
      wildcardPermissions: wildcardCount,
    };
  }

  /**
   * Find permissions that match resource and action
   */
  private findMatchingPermissions(
    permissions: ResolvedPermission[],
    resource: string,
    action: PermissionAction
  ): ResolvedPermission[] {
    return permissions.filter(permission => {
      // Check resource match (exact match or wildcard)
      const resourceMatch = permission.resource === resource || permission.resource === '*';
      
      // Check action match (exact match or 'manage' permission)
      const actionMatch = 
        permission.effectiveActions.includes(action) ||
        permission.effectiveActions.includes('manage');

      return resourceMatch && actionMatch;
    });
  }

  /**
   * Evaluate permission conditions
   */
  private evaluatePermissionConditions(
    permission: ResolvedPermission,
    context: AuthorizationContext
  ): boolean {
    // If no conditions, permission is granted
    if (!permission.conditions || permission.conditions.length === 0) {
      return true;
    }

    // Evaluate all conditions (AND logic)
    return evaluateConditions(permission.conditions, context);
  }

  /**
   * Create a new permission
   */
  async createPermission(permissionData: Omit<Permission, 'id'>): Promise<Permission> {
    const permission: Permission = {
      ...permissionData,
      id: `perm-${Date.now()}`,
    };

    // In a real implementation, this would save to database
    return permission;
  }

  /**
   * Update an existing permission
   */
  async updatePermission(
    permissionId: UUID,
    updates: Partial<Permission>
  ): Promise<Permission> {
    // In a real implementation, this would update in database
    throw new Error('Not implemented');
  }

  /**
   * Delete a permission
   */
  async deletePermission(permissionId: UUID): Promise<void> {
    // In a real implementation, this would delete from database
    throw new Error('Not implemented');
  }

  /**
   * Validate permission structure
   */
  validatePermission(permission: Partial<Permission>): string[] {
    const errors: string[] = [];

    if (!permission.name) {
      errors.push('Permission name is required');
    }

    if (!permission.resource) {
      errors.push('Permission resource is required');
    }

    if (!permission.actions || permission.actions.length === 0) {
      errors.push('Permission must have at least one action');
    }

    if (permission.level && !['feature', 'resource', 'action'].includes(permission.level)) {
      errors.push('Permission level must be feature, resource, or action');
    }

    return errors;
  }

  /**
   * Create RBAC error
   */
  private createError(code: string, message: string): RBACError {
    return {
      code,
      message,
      statusCode: 403,
    };
  }
}
