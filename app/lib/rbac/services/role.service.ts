/**
 * Role Service
 * Handles role management, resolution, and hierarchy
 */

import type {
  Role,
  UserTenantRole,
  RoleHierarchy,
  UUID,
  Permission,
  RBACError,
} from '../types';
import { DEFAULT_ROLE_HIERARCHY, SYSTEM_PERMISSIONS, RBAC_ERROR_MESSAGES } from '../constants';
import { getEffectiveRole, getInheritedRoles } from '../utils';

// Mock data store for roles
class MockRoleStore {
  private roles: Map<string, Role> = new Map();
  private userTenantRoles: Map<string, UserTenantRole[]> = new Map();

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    // Create system roles with permissions
    const systemRoles: Role[] = [
      {
        id: 'role-super-admin',
        name: 'super_admin',
        displayName: 'Super Administrator',
        description: 'Full system access with all permissions',
        level: 0,
        permissions: Object.values(SYSTEM_PERMISSIONS).map(p => ({ ...p, id: `perm-${p.name}` })),
        isSystemRole: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'role-admin',
        name: 'admin',
        displayName: 'Administrator',
        description: 'Administrative access with user and content management',
        level: 1,
        permissions: [
          { ...SYSTEM_PERMISSIONS['users.manage'], id: 'perm-users.manage' },
          { ...SYSTEM_PERMISSIONS['roles.manage'], id: 'perm-roles.manage' },
          { ...SYSTEM_PERMISSIONS['content.create'], id: 'perm-content.create' },
          { ...SYSTEM_PERMISSIONS['content.read'], id: 'perm-content.read' },
          { ...SYSTEM_PERMISSIONS['content.update'], id: 'perm-content.update' },
          { ...SYSTEM_PERMISSIONS['content.delete'], id: 'perm-content.delete' },
          { ...SYSTEM_PERMISSIONS['reports.create'], id: 'perm-reports.create' },
          { ...SYSTEM_PERMISSIONS['reports.read'], id: 'perm-reports.read' },
          { ...SYSTEM_PERMISSIONS['dashboard.read'], id: 'perm-dashboard.read' },
          { ...SYSTEM_PERMISSIONS['settings.manage'], id: 'perm-settings.manage' },
        ],
        inheritsFrom: ['role-editor'],
        isSystemRole: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'role-editor',
        name: 'editor',
        displayName: 'Editor',
        description: 'Content editing and creation permissions',
        level: 2,
        permissions: [
          { ...SYSTEM_PERMISSIONS['content.create'], id: 'perm-content.create' },
          { ...SYSTEM_PERMISSIONS['content.read'], id: 'perm-content.read' },
          { ...SYSTEM_PERMISSIONS['content.update'], id: 'perm-content.update' },
          { ...SYSTEM_PERMISSIONS['content.delete'], id: 'perm-content.delete' },
          { ...SYSTEM_PERMISSIONS['reports.create'], id: 'perm-reports.create' },
          { ...SYSTEM_PERMISSIONS['reports.read'], id: 'perm-reports.read' },
          { ...SYSTEM_PERMISSIONS['dashboard.read'], id: 'perm-dashboard.read' },
        ],
        inheritsFrom: ['role-viewer'],
        isSystemRole: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'role-viewer',
        name: 'viewer',
        displayName: 'Viewer',
        description: 'Read-only access to content and reports',
        level: 3,
        permissions: [
          { ...SYSTEM_PERMISSIONS['content.read'], id: 'perm-content.read' },
          { ...SYSTEM_PERMISSIONS['reports.read'], id: 'perm-reports.read' },
          { ...SYSTEM_PERMISSIONS['dashboard.read'], id: 'perm-dashboard.read' },
        ],
        isSystemRole: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    systemRoles.forEach(role => this.roles.set(role.id, role));

    // Create user-tenant-role assignments
    const userTenantRoles: UserTenantRole[] = [
      {
        id: 'utr-1',
        userId: 'user-1',
        tenantId: 'tenant-1',
        roleId: 'role-admin',
        isActive: true,
        assignedBy: 'system',
        assignedAt: new Date().toISOString(),
      },
      {
        id: 'utr-2',
        userId: 'user-2',
        tenantId: 'tenant-1',
        roleId: 'role-editor',
        isActive: true,
        assignedBy: 'user-1',
        assignedAt: new Date().toISOString(),
      },
      {
        id: 'utr-3',
        userId: 'user-3',
        tenantId: 'tenant-2',
        roleId: 'role-viewer',
        isActive: true,
        assignedBy: 'system',
        assignedAt: new Date().toISOString(),
      },
    ];

    // Group by user ID for easier lookup
    userTenantRoles.forEach(utr => {
      const userRoles = this.userTenantRoles.get(utr.userId) || [];
      userRoles.push(utr);
      this.userTenantRoles.set(utr.userId, userRoles);
    });
  }

  async getRoleById(roleId: string): Promise<Role | null> {
    return this.roles.get(roleId) || null;
  }

  async getRolesByIds(roleIds: string[]): Promise<Role[]> {
    const roles: Role[] = [];
    for (const roleId of roleIds) {
      const role = this.roles.get(roleId);
      if (role) {
        roles.push(role);
      }
    }
    return roles;
  }

  async getUserTenantRoles(userId: string, tenantId?: string): Promise<UserTenantRole[]> {
    const userRoles = this.userTenantRoles.get(userId) || [];
    
    if (tenantId) {
      return userRoles.filter(utr => utr.tenantId === tenantId && utr.isActive);
    }
    
    return userRoles.filter(utr => utr.isActive);
  }

  async getAllRoles(): Promise<Role[]> {
    return Array.from(this.roles.values());
  }

  async createRole(role: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    const newRole: Role = {
      ...role,
      id: `role-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    this.roles.set(newRole.id, newRole);
    return newRole;
  }

  async updateRole(roleId: string, updates: Partial<Role>): Promise<Role | null> {
    const role = this.roles.get(roleId);
    if (!role) return null;

    const updatedRole = {
      ...role,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.roles.set(roleId, updatedRole);
    return updatedRole;
  }

  async deleteRole(roleId: string): Promise<boolean> {
    return this.roles.delete(roleId);
  }

  async assignRoleToUser(userTenantRole: Omit<UserTenantRole, 'id' | 'assignedAt'>): Promise<UserTenantRole> {
    const newAssignment: UserTenantRole = {
      ...userTenantRole,
      id: `utr-${Date.now()}`,
      assignedAt: new Date().toISOString(),
    };

    const userRoles = this.userTenantRoles.get(userTenantRole.userId) || [];
    userRoles.push(newAssignment);
    this.userTenantRoles.set(userTenantRole.userId, userRoles);

    return newAssignment;
  }

  async removeRoleFromUser(userId: string, tenantId: string, roleId: string): Promise<boolean> {
    const userRoles = this.userTenantRoles.get(userId) || [];
    const index = userRoles.findIndex(utr => 
      utr.tenantId === tenantId && utr.roleId === roleId
    );

    if (index !== -1) {
      userRoles.splice(index, 1);
      this.userTenantRoles.set(userId, userRoles);
      return true;
    }

    return false;
  }
}

export class RoleService {
  private roleStore: MockRoleStore;

  constructor() {
    this.roleStore = new MockRoleStore();
  }

  /**
   * Get user's roles for a specific tenant
   */
  async getUserRolesForTenant(userId: UUID, tenantId: UUID): Promise<Role[]> {
    const userTenantRoles = await this.roleStore.getUserTenantRoles(userId, tenantId);
    const roleIds = userTenantRoles.map(utr => utr.roleId);
    return this.roleStore.getRolesByIds(roleIds);
  }

  /**
   * Get user's roles across all tenants
   */
  async getUserRoles(userId: UUID): Promise<UserTenantRole[]> {
    return this.roleStore.getUserTenantRoles(userId);
  }

  /**
   * Get effective role for user in tenant (highest priority role)
   */
  async getEffectiveUserRole(userId: UUID, tenantId: UUID): Promise<Role | null> {
    const roles = await this.getUserRolesForTenant(userId, tenantId);
    return getEffectiveRole(roles, DEFAULT_ROLE_HIERARCHY);
  }

  /**
   * Check if user has a specific role in tenant
   */
  async userHasRole(userId: UUID, tenantId: UUID, roleName: string): Promise<boolean> {
    const roles = await this.getUserRolesForTenant(userId, tenantId);
    return roles.some(role => role.name === roleName);
  }

  /**
   * Check if user has any of the specified roles in tenant
   */
  async userHasAnyRole(userId: UUID, tenantId: UUID, roleNames: string[]): Promise<boolean> {
    const roles = await this.getUserRolesForTenant(userId, tenantId);
    const userRoleNames = roles.map(role => role.name);
    return roleNames.some(roleName => userRoleNames.includes(roleName));
  }

  /**
   * Check if user has all of the specified roles in tenant
   */
  async userHasAllRoles(userId: UUID, tenantId: UUID, roleNames: string[]): Promise<boolean> {
    const roles = await this.getUserRolesForTenant(userId, tenantId);
    const userRoleNames = roles.map(role => role.name);
    return roleNames.every(roleName => userRoleNames.includes(roleName));
  }

  /**
   * Get role by ID
   */
  async getRoleById(roleId: UUID): Promise<Role | null> {
    return this.roleStore.getRoleById(roleId);
  }

  /**
   * Get role by name
   */
  async getRoleByName(roleName: string): Promise<Role | null> {
    const allRoles = await this.roleStore.getAllRoles();
    return allRoles.find(role => role.name === roleName) || null;
  }

  /**
   * Get all available roles
   */
  async getAllRoles(): Promise<Role[]> {
    return this.roleStore.getAllRoles();
  }

  /**
   * Get system roles only
   */
  async getSystemRoles(): Promise<Role[]> {
    const allRoles = await this.roleStore.getAllRoles();
    return allRoles.filter(role => role.isSystemRole);
  }

  /**
   * Create a new role
   */
  async createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    return this.roleStore.createRole(roleData);
  }

  /**
   * Update an existing role
   */
  async updateRole(roleId: UUID, updates: Partial<Role>): Promise<Role> {
    const updatedRole = await this.roleStore.updateRole(roleId, updates);
    if (!updatedRole) {
      throw this.createError('ROLE_NOT_FOUND', RBAC_ERROR_MESSAGES.ROLE_NOT_FOUND);
    }
    return updatedRole;
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: UUID): Promise<void> {
    const success = await this.roleStore.deleteRole(roleId);
    if (!success) {
      throw this.createError('ROLE_NOT_FOUND', RBAC_ERROR_MESSAGES.ROLE_NOT_FOUND);
    }
  }

  /**
   * Assign role to user in tenant
   */
  async assignRoleToUser(
    userId: UUID,
    tenantId: UUID,
    roleId: UUID,
    assignedBy: UUID,
    expiresAt?: string
  ): Promise<UserTenantRole> {
    return this.roleStore.assignRoleToUser({
      userId,
      tenantId,
      roleId,
      isActive: true,
      assignedBy,
      expiresAt,
    });
  }

  /**
   * Remove role from user in tenant
   */
  async removeRoleFromUser(userId: UUID, tenantId: UUID, roleId: UUID): Promise<void> {
    const success = await this.roleStore.removeRoleFromUser(userId, tenantId, roleId);
    if (!success) {
      throw this.createError('ROLE_NOT_FOUND', 'Role assignment not found');
    }
  }

  /**
   * Get role hierarchy
   */
  getRoleHierarchy(): RoleHierarchy {
    return DEFAULT_ROLE_HIERARCHY;
  }

  /**
   * Get inherited roles for a role
   */
  getInheritedRoles(roleName: string): string[] {
    return getInheritedRoles(roleName, DEFAULT_ROLE_HIERARCHY);
  }

  /**
   * Create RBAC error
   */
  private createError(code: string, message: string): RBACError {
    return {
      code,
      message,
      statusCode: 404,
    };
  }
}
