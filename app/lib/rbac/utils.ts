/**
 * RBAC Utility Functions
 */

import type {
  Permission,
  PermissionAction,
  PermissionCondition,
  Role,
  UserContext,
  AuthorizationContext,
  AuthorizationResult,
  ResolvedPermission,
  RoleHierarchy,
} from './types';

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  userContext: UserContext,
  resource: string,
  action: PermissionAction,
  resourceId?: string
): boolean {
  return userContext.resolvedPermissions.some(permission => {
    // Check if permission matches resource and action
    if (permission.resource !== resource && permission.resource !== '*') {
      return false;
    }

    if (!permission.effectiveActions.includes(action) && !permission.effectiveActions.includes('manage' as PermissionAction)) {
      return false;
    }

    // Check conditions if present
    if (permission.conditions && permission.conditions.length > 0) {
      return evaluateConditions(permission.conditions, {
        user: userContext,
        resource,
        action,
        resourceId,
      });
    }

    return true;
  });
}

/**
 * Check if a user has any of the specified roles
 */
export function hasRole(userContext: UserContext, roles: string[]): boolean {
  const userRoles = userContext.tenantRoles.map(tr => tr.roleId);
  return roles.some(role => userRoles.includes(role));
}

/**
 * Check if a user has all of the specified roles
 */
export function hasAllRoles(userContext: UserContext, roles: string[]): boolean {
  const userRoles = userContext.tenantRoles.map(tr => tr.roleId);
  return roles.every(role => userRoles.includes(role));
}

/**
 * Get the highest priority role for a user in the current tenant
 */
export function getEffectiveRole(roles: Role[], hierarchy: RoleHierarchy): Role | null {
  if (roles.length === 0) return null;

  // Sort roles by hierarchy level (lower level = higher priority)
  const sortedRoles = roles.sort((a, b) => {
    const aLevel = hierarchy[a.name]?.level ?? 999;
    const bLevel = hierarchy[b.name]?.level ?? 999;
    return aLevel - bLevel;
  });

  return sortedRoles[0];
}

/**
 * Resolve permissions from roles with inheritance
 */
export function resolvePermissions(
  roles: Role[],
  tenantId: string,
  hierarchy: RoleHierarchy
): ResolvedPermission[] {
  const resolvedPermissions: ResolvedPermission[] = [];
  const processedRoles = new Set<string>();

  function processRole(role: Role, source: 'role' | 'inherited' = 'role') {
    if (processedRoles.has(role.id)) return;
    processedRoles.add(role.id);

    // Add role's direct permissions
    role.permissions.forEach(permission => {
      resolvedPermissions.push({
        ...permission,
        source,
        sourceId: role.id,
        tenantId,
        effectiveActions: permission.actions,
      });
    });

    // Process inherited roles
    if (role.inheritsFrom && role.inheritsFrom.length > 0) {
      role.inheritsFrom.forEach(parentRoleId => {
        const parentRole = roles.find(r => r.id === parentRoleId);
        if (parentRole) {
          processRole(parentRole, 'inherited');
        }
      });
    }
  }

  roles.forEach(role => processRole(role));

  // Remove duplicates and merge permissions
  return mergeDuplicatePermissions(resolvedPermissions);
}

/**
 * Merge duplicate permissions and combine actions
 */
function mergeDuplicatePermissions(permissions: ResolvedPermission[]): ResolvedPermission[] {
  const permissionMap = new Map<string, ResolvedPermission>();

  permissions.forEach(permission => {
    const key = `${permission.resource}:${permission.name}`;
    
    if (permissionMap.has(key)) {
      const existing = permissionMap.get(key)!;
      // Merge actions
      const combinedActions = Array.from(new Set([
        ...existing.effectiveActions,
        ...permission.effectiveActions,
      ]));
      
      permissionMap.set(key, {
        ...existing,
        effectiveActions: combinedActions,
        // Keep the highest priority source
        source: existing.source === 'role' ? 'role' : permission.source,
      });
    } else {
      permissionMap.set(key, permission);
    }
  });

  return Array.from(permissionMap.values());
}

/**
 * Evaluate permission conditions
 */
export function evaluateConditions(
  conditions: PermissionCondition[],
  context: AuthorizationContext
): boolean {
  return conditions.every(condition => evaluateCondition(condition, context));
}

/**
 * Evaluate a single permission condition
 */
function evaluateCondition(
  condition: PermissionCondition,
  context: AuthorizationContext
): boolean {
  let contextValue: any;

  // Get the value from the appropriate context
  switch (condition.context) {
    case 'user':
      contextValue = getNestedValue(context.user.user, condition.field);
      break;
    case 'tenant':
      contextValue = getNestedValue(context.user.currentTenant, condition.field);
      break;
    case 'resource':
      contextValue = context.additionalContext?.[condition.field];
      break;
    case 'request':
      contextValue = context.additionalContext?.[condition.field];
      break;
    default:
      contextValue = context.additionalContext?.[condition.field];
  }

  // Evaluate the condition based on operator
  switch (condition.operator) {
    case 'eq':
      return contextValue === condition.value;
    case 'ne':
      return contextValue !== condition.value;
    case 'in':
      return Array.isArray(condition.value) && condition.value.includes(contextValue);
    case 'nin':
      return Array.isArray(condition.value) && !condition.value.includes(contextValue);
    case 'gt':
      return contextValue > condition.value;
    case 'gte':
      return contextValue >= condition.value;
    case 'lt':
      return contextValue < condition.value;
    case 'lte':
      return contextValue <= condition.value;
    case 'contains':
      return typeof contextValue === 'string' && contextValue.includes(condition.value);
    case 'startsWith':
      return typeof contextValue === 'string' && contextValue.startsWith(condition.value);
    case 'endsWith':
      return typeof contextValue === 'string' && contextValue.endsWith(condition.value);
    default:
      return false;
  }
}

/**
 * Get nested object value by dot notation path
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Check if a role can perform an action on a resource
 */
export function canRolePerformAction(
  role: Role,
  resource: string,
  action: PermissionAction
): boolean {
  return role.permissions.some(permission => {
    if (permission.resource !== resource && permission.resource !== '*') {
      return false;
    }
    return permission.actions.includes(action) || permission.actions.includes('manage');
  });
}

/**
 * Get all permissions for a specific resource
 */
export function getResourcePermissions(
  userContext: UserContext,
  resource: string
): ResolvedPermission[] {
  return userContext.resolvedPermissions.filter(
    permission => permission.resource === resource || permission.resource === '*'
  );
}

/**
 * Check if user is active and tenant is active
 */
export function isUserAndTenantActive(userContext: UserContext): boolean {
  return userContext.user.isActive && userContext.isAuthenticated;
}

/**
 * Generate authorization result
 */
export function createAuthorizationResult(
  allowed: boolean,
  reason?: string,
  requiredPermissions?: string[],
  missingPermissions?: string[]
): AuthorizationResult {
  return {
    allowed,
    reason,
    requiredPermissions,
    missingPermissions,
  };
}

/**
 * Check if a permission is a wildcard permission
 */
export function isWildcardPermission(permission: Permission): boolean {
  return permission.resource === '*' || permission.actions.includes('manage');
}

/**
 * Format role display name
 */
export function formatRoleDisplayName(roleName: string): string {
  return roleName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Check if role has higher or equal level than another role
 */
export function hasHigherOrEqualRole(
  userRole: string,
  requiredRole: string,
  hierarchy: RoleHierarchy
): boolean {
  const userLevel = hierarchy[userRole]?.level ?? 999;
  const requiredLevel = hierarchy[requiredRole]?.level ?? 999;
  return userLevel <= requiredLevel; // Lower level number = higher privilege
}

/**
 * Get all inherited roles for a role
 */
export function getInheritedRoles(roleName: string, hierarchy: RoleHierarchy): string[] {
  const role = hierarchy[roleName];
  if (!role || !role.inherits) return [];

  const inherited: string[] = [];
  const visited = new Set<string>();

  function collectInherited(currentRole: string) {
    if (visited.has(currentRole)) return;
    visited.add(currentRole);

    const roleConfig = hierarchy[currentRole];
    if (roleConfig?.inherits) {
      roleConfig.inherits.forEach(parentRole => {
        inherited.push(parentRole);
        collectInherited(parentRole);
      });
    }
  }

  collectInherited(roleName);
  return inherited;
}
