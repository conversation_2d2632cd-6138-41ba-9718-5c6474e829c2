/**
 * RBAC System - Main Export File
 * Exports all types, services, utilities, and constants
 */

// Types
export type {
  UUID,
  Timestamp,
  PermissionLevel,
  PermissionAction,
  Tenant,
  TenantSettings,
  TenantFeature,
  Permission,
  PermissionCondition,
  Role,
  RoleHierarchy,
  User,
  UserTenantRole,
  UserContext,
  ResolvedPermission,
  AuthorizationContext,
  AuthorizationResult,
  RoutePermission,
  ProtectedRouteConfig,
  AuthenticationRequest,
  AuthenticationResponse,
  RoleAssignmentRequest,
  RBACError,
  RBACErrorCode,
  RBACConfig,
  WithRBACProps,
  PermissionGuardProps,
  RoleGuardProps,
  PermissionCheck,
  RoleResolver,
  PermissionResolver,
} from './types';

// Constants
export {
  DEFAULT_RBAC_CONFIG,
  DEFAULT_ROLE_HIERARCHY,
  SYSTEM_PERMISSIONS,
  RBAC_ERROR_MESSAGES,
  ROUTE_PATTERNS,
  REDIRECT_PATHS,
  CACHE_KEYS,
  HTTP_STATUS,
} from './constants';

// Utilities
export {
  hasPermission,
  hasRole,
  hasAllRoles,
  getEffectiveRole,
  resolvePermissions,
  evaluateConditions,
  canRolePerformAction,
  getResourcePermissions,
  isUserAndTenantActive,
  createAuthorizationResult,
  isWildcardPermission,
  formatRoleDisplayName,
  hasHigherOrEqualRole,
  getInheritedRoles,
} from './utils';

// Services
export { AuthService } from './services/auth.service';
export { RoleService } from './services/role.service';
export { PermissionService } from './services/permission.service';
export { RBACService, rbacService } from './services/rbac.service';

// Re-export for convenience
export const RBAC = {
  service: rbacService,
  constants: {
    DEFAULT_RBAC_CONFIG,
    DEFAULT_ROLE_HIERARCHY,
    SYSTEM_PERMISSIONS,
    RBAC_ERROR_MESSAGES,
    ROUTE_PATTERNS,
    REDIRECT_PATHS,
    CACHE_KEYS,
    HTTP_STATUS,
  },
};
