/**
 * RBAC System Constants and Default Configurations
 */

import type { RBACConfig, RoleHierarchy, Permission } from './types';

// Default RBAC configuration
export const DEFAULT_RBAC_CONFIG: RBACConfig = {
  tokenExpiry: 3600, // 1 hour
  refreshTokenExpiry: 604800, // 7 days
  defaultRole: 'viewer',
  systemRoles: ['super_admin', 'admin', 'editor', 'viewer'],
  enableRoleHierarchy: true,
  enablePermissionInheritance: true,
  enableTenantIsolation: true,
  cachePermissions: true,
  cacheTTL: 300, // 5 minutes
};

// System role hierarchy (level 0 = highest privilege)
export const DEFAULT_ROLE_HIERARCHY: RoleHierarchy = {
  super_admin: {
    level: 0,
    inherits: [],
    permissions: ['*'], // All permissions
  },
  admin: {
    level: 1,
    inherits: ['editor'],
    permissions: [
      'users.manage',
      'roles.manage',
      'tenants.manage',
      'settings.manage',
    ],
  },
  editor: {
    level: 2,
    inherits: ['viewer'],
    permissions: [
      'content.create',
      'content.update',
      'content.delete',
      'reports.create',
    ],
  },
  viewer: {
    level: 3,
    inherits: [],
    permissions: [
      'content.read',
      'reports.read',
      'dashboard.read',
    ],
  },
};

// Default system permissions
export const SYSTEM_PERMISSIONS: Record<string, Omit<Permission, 'id'>> = {
  // User management
  'users.create': {
    name: 'users.create',
    description: 'Create new users',
    level: 'resource',
    resource: 'users',
    actions: ['create'],
  },
  'users.read': {
    name: 'users.read',
    description: 'View user information',
    level: 'resource',
    resource: 'users',
    actions: ['read'],
  },
  'users.update': {
    name: 'users.update',
    description: 'Update user information',
    level: 'resource',
    resource: 'users',
    actions: ['update'],
  },
  'users.delete': {
    name: 'users.delete',
    description: 'Delete users',
    level: 'resource',
    resource: 'users',
    actions: ['delete'],
  },
  'users.manage': {
    name: 'users.manage',
    description: 'Full user management access',
    level: 'resource',
    resource: 'users',
    actions: ['create', 'read', 'update', 'delete', 'manage'],
  },

  // Role management
  'roles.create': {
    name: 'roles.create',
    description: 'Create new roles',
    level: 'resource',
    resource: 'roles',
    actions: ['create'],
  },
  'roles.read': {
    name: 'roles.read',
    description: 'View role information',
    level: 'resource',
    resource: 'roles',
    actions: ['read'],
  },
  'roles.update': {
    name: 'roles.update',
    description: 'Update role information',
    level: 'resource',
    resource: 'roles',
    actions: ['update'],
  },
  'roles.delete': {
    name: 'roles.delete',
    description: 'Delete roles',
    level: 'resource',
    resource: 'roles',
    actions: ['delete'],
  },
  'roles.manage': {
    name: 'roles.manage',
    description: 'Full role management access',
    level: 'resource',
    resource: 'roles',
    actions: ['create', 'read', 'update', 'delete', 'manage'],
  },

  // Content management
  'content.create': {
    name: 'content.create',
    description: 'Create new content',
    level: 'resource',
    resource: 'content',
    actions: ['create'],
  },
  'content.read': {
    name: 'content.read',
    description: 'View content',
    level: 'resource',
    resource: 'content',
    actions: ['read'],
  },
  'content.update': {
    name: 'content.update',
    description: 'Update content',
    level: 'resource',
    resource: 'content',
    actions: ['update'],
  },
  'content.delete': {
    name: 'content.delete',
    description: 'Delete content',
    level: 'resource',
    resource: 'content',
    actions: ['delete'],
  },

  // Dashboard access
  'dashboard.read': {
    name: 'dashboard.read',
    description: 'View dashboard',
    level: 'feature',
    resource: 'dashboard',
    actions: ['read'],
  },

  // Reports
  'reports.create': {
    name: 'reports.create',
    description: 'Create reports',
    level: 'resource',
    resource: 'reports',
    actions: ['create'],
  },
  'reports.read': {
    name: 'reports.read',
    description: 'View reports',
    level: 'resource',
    resource: 'reports',
    actions: ['read'],
  },

  // Settings
  'settings.read': {
    name: 'settings.read',
    description: 'View settings',
    level: 'resource',
    resource: 'settings',
    actions: ['read'],
  },
  'settings.update': {
    name: 'settings.update',
    description: 'Update settings',
    level: 'resource',
    resource: 'settings',
    actions: ['update'],
  },
  'settings.manage': {
    name: 'settings.manage',
    description: 'Full settings management',
    level: 'resource',
    resource: 'settings',
    actions: ['read', 'update', 'manage'],
  },

  // Tenant management
  'tenants.create': {
    name: 'tenants.create',
    description: 'Create new tenants',
    level: 'resource',
    resource: 'tenants',
    actions: ['create'],
  },
  'tenants.read': {
    name: 'tenants.read',
    description: 'View tenant information',
    level: 'resource',
    resource: 'tenants',
    actions: ['read'],
  },
  'tenants.update': {
    name: 'tenants.update',
    description: 'Update tenant information',
    level: 'resource',
    resource: 'tenants',
    actions: ['update'],
  },
  'tenants.delete': {
    name: 'tenants.delete',
    description: 'Delete tenants',
    level: 'resource',
    resource: 'tenants',
    actions: ['delete'],
  },
  'tenants.manage': {
    name: 'tenants.manage',
    description: 'Full tenant management access',
    level: 'resource',
    resource: 'tenants',
    actions: ['create', 'read', 'update', 'delete', 'manage'],
  },
};

// Error messages
export const RBAC_ERROR_MESSAGES = {
  UNAUTHORIZED: 'Authentication required',
  FORBIDDEN: 'Access denied - insufficient permissions',
  INVALID_TOKEN: 'Invalid authentication token',
  TOKEN_EXPIRED: 'Authentication token has expired',
  TENANT_NOT_FOUND: 'Tenant not found or access denied',
  ROLE_NOT_FOUND: 'Role not found',
  PERMISSION_DENIED: 'Permission denied for this action',
  INVALID_CREDENTIALS: 'Invalid email or password',
  USER_INACTIVE: 'User account is inactive',
  TENANT_INACTIVE: 'Tenant account is inactive',
} as const;

// Route patterns for different permission levels
export const ROUTE_PATTERNS = {
  PUBLIC: '/public/*',
  AUTH: '/auth/*',
  DASHBOARD: '/dashboard/*',
  ADMIN: '/admin/*',
  SETTINGS: '/settings/*',
  USERS: '/users/*',
  REPORTS: '/reports/*',
} as const;

// Default redirect paths
export const REDIRECT_PATHS = {
  LOGIN: '/auth/login',
  UNAUTHORIZED: '/auth/unauthorized',
  FORBIDDEN: '/auth/forbidden',
  DEFAULT_AFTER_LOGIN: '/dashboard',
  DEFAULT_AFTER_LOGOUT: '/auth/login',
} as const;

// Cache keys for permission caching
export const CACHE_KEYS = {
  USER_PERMISSIONS: (userId: string, tenantId: string) => `permissions:${userId}:${tenantId}`,
  USER_ROLES: (userId: string, tenantId: string) => `roles:${userId}:${tenantId}`,
  TENANT_CONFIG: (tenantId: string) => `tenant:${tenantId}`,
  ROLE_HIERARCHY: (tenantId: string) => `hierarchy:${tenantId}`,
} as const;

// HTTP status codes for RBAC errors
export const HTTP_STATUS = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
} as const;
