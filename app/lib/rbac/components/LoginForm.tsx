/**
 * Login Form Component
 * Provides a login form with Ant Design styling
 */

import React, { useState } from 'react';
import { Form, Input, Button, Alert, Select, Card, Typography } from 'antd';
import { UserOutlined, LockOutlined, GlobalOutlined } from '@ant-design/icons';
import { useAuth } from './AuthProvider';

const { Title } = Typography;
const { Option } = Select;

interface LoginFormProps {
  onSuccess?: () => void;
  showTenantSelector?: boolean;
  availableTenants?: Array<{ slug: string; name: string }>;
  title?: string;
  subtitle?: string;
}

export function LoginForm({
  onSuccess,
  showTenantSelector = false,
  availableTenants = [],
  title = 'Sign In',
  subtitle = 'Welcome back! Please sign in to your account.',
}: LoginFormProps) {
  const { login, isLoading, error } = useAuth();
  const [form] = Form.useForm();

  const handleSubmit = async (values: {
    email: string;
    password: string;
    tenantSlug?: string;
  }) => {
    const success = await login(values.email, values.password, values.tenantSlug);
    
    if (success) {
      onSuccess?.();
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <div className="text-center mb-6">
        <Title level={2}>{title}</Title>
        {subtitle && (
          <Typography.Text type="secondary">{subtitle}</Typography.Text>
        )}
      </div>

      {error && (
        <Alert
          message="Login Failed"
          description={error.message}
          type="error"
          showIcon
          className="mb-4"
        />
      )}

      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        layout="vertical"
        size="large"
      >
        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: 'Please input your email!' },
            { type: 'email', message: 'Please enter a valid email!' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="Enter your email"
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[{ required: true, message: 'Please input your password!' }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter your password"
            autoComplete="current-password"
          />
        </Form.Item>

        {showTenantSelector && availableTenants.length > 0 && (
          <Form.Item
            name="tenantSlug"
            label="Organization"
            rules={[{ required: true, message: 'Please select an organization!' }]}
          >
            <Select
              placeholder="Select your organization"
              prefix={<GlobalOutlined />}
            >
              {availableTenants.map(tenant => (
                <Option key={tenant.slug} value={tenant.slug}>
                  {tenant.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full"
          >
            {isLoading ? 'Signing In...' : 'Sign In'}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}

/**
 * Quick Login Component for Demo Purposes
 */
export function QuickLoginDemo() {
  const { login, isLoading } = useAuth();

  const demoUsers = [
    { email: '<EMAIL>', password: 'admin123', role: 'Admin', tenant: 'Acme Corp' },
    { email: '<EMAIL>', password: 'editor123', role: 'Editor', tenant: 'Acme Corp' },
    { email: '<EMAIL>', password: 'viewer123', role: 'Viewer', tenant: 'Beta Industries' },
  ];

  const handleDemoLogin = async (email: string, password: string) => {
    await login(email, password);
  };

  return (
    <Card title="Demo Login" className="w-full max-w-md mx-auto mt-4">
      <Typography.Text type="secondary" className="block mb-4">
        Click any button below to login as a demo user:
      </Typography.Text>
      
      <div className="space-y-2">
        {demoUsers.map((user, index) => (
          <Button
            key={index}
            block
            loading={isLoading}
            onClick={() => handleDemoLogin(user.email, user.password)}
            className="text-left"
          >
            <div className="flex justify-between items-center w-full">
              <span>{user.role}</span>
              <Typography.Text type="secondary" className="text-xs">
                {user.tenant}
              </Typography.Text>
            </div>
          </Button>
        ))}
      </div>
    </Card>
  );
}
