/**
 * Authentication Provider Component
 * Provides user context and authentication state to the application
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import { useLoaderData, useRevalidator } from "react-router";
import type { UserContext, RBACError } from "../types";

interface AuthContextType {
  user: UserContext | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: RBACError | null;
  login: (
    email: string,
    password: string,
    tenantSlug?: string
  ) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshAuth: () => void;
  hasPermission: (resource: string, action: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const loaderData = useLoaderData() as {
    user: UserContext | null;
    isAuthenticated: boolean;
    error?: string;
  };

  const revalidator = useRevalidator();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<RBACError | null>(null);

  // Update error state when loader data changes
  useEffect(() => {
    if (loaderData.error) {
      setError({
        code: "AUTHENTICATION_ERROR",
        message: loaderData.error,
        statusCode: 401,
      });
    } else {
      setError(null);
    }
  }, [loaderData.error]);

  /**
   * Login function
   */
  const login = async (
    email: string,
    password: string,
    tenantSlug?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("email", email);
      formData.append("password", password);
      if (tenantSlug) {
        formData.append("tenantSlug", tenantSlug);
      }

      const response = await fetch("/auth/login", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // Revalidate to get updated user context
        revalidator.revalidate();
        return true;
      } else {
        setError({
          code: "LOGIN_FAILED",
          message: result.error || "Login failed",
          statusCode: response.status,
        });
        return false;
      }
    } catch (err) {
      setError({
        code: "NETWORK_ERROR",
        message: "Network error during login",
        statusCode: 500,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logout function
   */
  const logout = async (): Promise<void> => {
    setIsLoading(true);

    try {
      await fetch("/auth/logout", {
        method: "POST",
      });
    } catch (err) {
      console.error("Logout error:", err);
    } finally {
      setIsLoading(false);
      // Revalidate to clear user context
      revalidator.revalidate();
    }
  };

  /**
   * Refresh authentication state
   */
  const refreshAuth = () => {
    revalidator.revalidate();
  };

  /**
   * Check if user has a specific permission
   */
  const hasPermission = (resource: string, action: string): boolean => {
    if (!loaderData.user || !loaderData.isAuthenticated) {
      return false;
    }

    return loaderData.user.resolvedPermissions.some((permission) => {
      const resourceMatch =
        permission.resource === resource || permission.resource === "*";
      const actionMatch =
        permission.effectiveActions.includes(action as any) ||
        permission.effectiveActions.includes("manage" as any);

      return resourceMatch && actionMatch;
    });
  };

  /**
   * Check if user has a specific role
   */
  const hasRole = (role: string): boolean => {
    if (!loaderData.user || !loaderData.isAuthenticated) {
      return false;
    }

    return loaderData.user.tenantRoles.some(
      (tr) => tr.isActive && tr.roleId.includes(role) // Simple check - in real app, resolve role names
    );
  };

  /**
   * Check if user has any of the specified roles
   */
  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some((role) => hasRole(role));
  };

  /**
   * Check if user has all of the specified roles
   */
  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every((role) => hasRole(role));
  };

  const contextValue: AuthContextType = {
    user: loaderData.user,
    isAuthenticated: loaderData.isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    refreshAuth,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

/**
 * Hook to use authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

/**
 * Hook to get current user
 */
export function useUser(): UserContext | null {
  const { user } = useAuth();
  return user;
}

/**
 * Hook to check authentication status
 */
export function useIsAuthenticated(): boolean {
  const { isAuthenticated } = useAuth();
  return isAuthenticated;
}

/**
 * Hook to check permissions
 */
export function usePermissions() {
  const { hasPermission, hasRole, hasAnyRole, hasAllRoles } = useAuth();

  return {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    can: hasPermission, // Alias for hasPermission
  };
}

/**
 * Hook to get user's effective role
 */
export function useEffectiveRole() {
  const { user } = useAuth();
  return user?.effectiveRole || null;
}

/**
 * Hook to get current tenant
 */
export function useCurrentTenant() {
  const { user } = useAuth();
  return user?.currentTenant || null;
}
