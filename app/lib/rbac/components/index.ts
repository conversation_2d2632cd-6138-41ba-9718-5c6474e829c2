/**
 * RBAC Components - Main Export File
 * Exports all RBAC React components
 */

// Authentication Provider and Hooks
export {
  AuthProvider,
  useAuth,
  useUser,
  useIsAuthenticated,
  usePermissions,
  useEffectiveRole,
  useCurrentTenant,
} from './AuthProvider';

// Permission Guards
export {
  PermissionGuard,
  RoleGuard,
  AdminGuard,
  SuperAdminGuard,
  Can,
  Cannot,
  AuthenticatedGuard,
  UnauthenticatedGuard,
  ResourceOwnerGuard,
  ConditionalRender,
  PermissionSwitch,
  RoleSwitch,
} from './PermissionGuard';

// Authentication Forms
export {
  LoginForm,
  QuickLoginDemo,
} from './LoginForm';

// User Profile Components
export {
  UserProfileCard,
  UserRoles,
  PermissionsList,
  TenantSwitcher,
  UserStatusBadge,
} from './UserProfile';
