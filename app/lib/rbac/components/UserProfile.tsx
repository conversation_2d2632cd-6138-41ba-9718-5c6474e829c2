/**
 * User Profile Components
 * Display user information, roles, and permissions
 */

import React from 'react';
import {
  Card,
  Avatar,
  Typography,
  Tag,
  Descriptions,
  Badge,
  Button,
  Dropdown,
  Space,
  Divider,
} from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  SwapOutlined,
  CrownOutlined,
  ShieldOutlined,
} from '@ant-design/icons';
import { useAuth, useUser, useCurrentTenant, useEffectiveRole } from './AuthProvider';
import type { MenuProps } from 'antd';

const { Title, Text } = Typography;

/**
 * User Profile Card Component
 */
export function UserProfileCard() {
  const user = useUser();
  const currentTenant = useCurrentTenant();
  const effectiveRole = useEffectiveRole();
  const { logout } = useAuth();

  if (!user) {
    return null;
  }

  const handleLogout = async () => {
    await logout();
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile Settings',
    },
    {
      key: 'preferences',
      icon: <SettingOutlined />,
      label: 'Preferences',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  return (
    <Card className="w-full max-w-md">
      <div className="flex items-center space-x-4 mb-4">
        <Avatar
          size={64}
          src={user.user.avatar}
          icon={<UserOutlined />}
        />
        <div className="flex-1">
          <Title level={4} className="mb-1">
            {user.user.firstName} {user.user.lastName}
          </Title>
          <Text type="secondary">{user.user.email}</Text>
          <div className="mt-2">
            {effectiveRole && (
              <Tag color="blue" icon={<CrownOutlined />}>
                {effectiveRole.displayName}
              </Tag>
            )}
            {currentTenant && (
              <Tag color="green">
                {currentTenant.name}
              </Tag>
            )}
          </div>
        </div>
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
          <Button type="text" icon={<SettingOutlined />} />
        </Dropdown>
      </div>

      <Descriptions column={1} size="small">
        <Descriptions.Item label="Status">
          <Badge
            status={user.user.isActive ? 'success' : 'error'}
            text={user.user.isActive ? 'Active' : 'Inactive'}
          />
        </Descriptions.Item>
        <Descriptions.Item label="Email Verified">
          <Badge
            status={user.user.emailVerified ? 'success' : 'warning'}
            text={user.user.emailVerified ? 'Verified' : 'Unverified'}
          />
        </Descriptions.Item>
        {user.user.lastLoginAt && (
          <Descriptions.Item label="Last Login">
            {new Date(user.user.lastLoginAt).toLocaleDateString()}
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );
}

/**
 * User Roles Display Component
 */
export function UserRoles() {
  const user = useUser();
  const effectiveRole = useEffectiveRole();

  if (!user) {
    return null;
  }

  return (
    <Card title="Roles & Permissions" className="w-full">
      <div className="mb-4">
        <Text strong>Effective Role:</Text>
        <div className="mt-2">
          {effectiveRole ? (
            <Tag color="blue" icon={<CrownOutlined />} className="text-sm">
              {effectiveRole.displayName}
            </Tag>
          ) : (
            <Text type="secondary">No role assigned</Text>
          )}
        </div>
      </div>

      <Divider />

      <div className="mb-4">
        <Text strong>All Roles:</Text>
        <div className="mt-2 space-y-2">
          {user.tenantRoles.length > 0 ? (
            user.tenantRoles.map((tenantRole) => (
              <div key={tenantRole.id} className="flex items-center justify-between">
                <Tag color="default">
                  Role ID: {tenantRole.roleId}
                </Tag>
                <Badge
                  status={tenantRole.isActive ? 'success' : 'error'}
                  text={tenantRole.isActive ? 'Active' : 'Inactive'}
                />
              </div>
            ))
          ) : (
            <Text type="secondary">No roles assigned</Text>
          )}
        </div>
      </div>

      <Divider />

      <div>
        <Text strong>Permissions Summary:</Text>
        <div className="mt-2">
          <Space wrap>
            <Tag icon={<ShieldOutlined />}>
              {user.resolvedPermissions.length} Permissions
            </Tag>
            <Tag>
              {new Set(user.resolvedPermissions.map(p => p.resource)).size} Resources
            </Tag>
          </Space>
        </div>
      </div>
    </Card>
  );
}

/**
 * Permissions List Component
 */
export function PermissionsList() {
  const user = useUser();

  if (!user) {
    return null;
  }

  const groupedPermissions = user.resolvedPermissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = [];
    }
    acc[permission.resource].push(permission);
    return acc;
  }, {} as Record<string, typeof user.resolvedPermissions>);

  return (
    <Card title="Detailed Permissions" className="w-full">
      {Object.keys(groupedPermissions).length > 0 ? (
        <div className="space-y-4">
          {Object.entries(groupedPermissions).map(([resource, permissions]) => (
            <div key={resource}>
              <Title level={5} className="mb-2">
                {resource === '*' ? 'All Resources' : resource}
              </Title>
              <div className="space-y-2">
                {permissions.map((permission, index) => (
                  <div key={`${permission.id}-${index}`} className="border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <Text strong>{permission.name}</Text>
                      <Tag color={permission.source === 'role' ? 'blue' : 'orange'}>
                        {permission.source}
                      </Tag>
                    </div>
                    <Text type="secondary" className="block mb-2">
                      {permission.description}
                    </Text>
                    <div>
                      <Text className="text-xs">Actions: </Text>
                      <Space wrap>
                        {permission.effectiveActions.map(action => (
                          <Tag key={action} size="small">
                            {action}
                          </Tag>
                        ))}
                      </Space>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Text type="secondary">No permissions assigned</Text>
      )}
    </Card>
  );
}

/**
 * Tenant Switcher Component
 */
export function TenantSwitcher() {
  const user = useUser();
  const currentTenant = useCurrentTenant();

  if (!user) {
    return null;
  }

  // In a real app, you would get available tenants from the user context
  const availableTenants = [currentTenant].filter(Boolean);

  const handleTenantSwitch = async (tenantId: string) => {
    // Implementation would switch tenant context
    console.log('Switching to tenant:', tenantId);
  };

  const tenantMenuItems: MenuProps['items'] = availableTenants.map(tenant => ({
    key: tenant!.id,
    label: tenant!.name,
    onClick: () => handleTenantSwitch(tenant!.id),
  }));

  return (
    <Dropdown menu={{ items: tenantMenuItems }} placement="bottomLeft">
      <Button icon={<SwapOutlined />}>
        <Space>
          {currentTenant?.name || 'Select Tenant'}
        </Space>
      </Button>
    </Dropdown>
  );
}

/**
 * User Status Badge Component
 */
export function UserStatusBadge() {
  const user = useUser();
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return <Badge status="error" text="Not Authenticated" />;
  }

  if (!user.user.isActive) {
    return <Badge status="error" text="Inactive" />;
  }

  if (!user.user.emailVerified) {
    return <Badge status="warning" text="Email Unverified" />;
  }

  return <Badge status="success" text="Active" />;
}
