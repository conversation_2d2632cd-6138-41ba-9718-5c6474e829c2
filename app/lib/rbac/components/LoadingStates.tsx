/**
 * Loading State Components
 * Provides various loading states for RBAC operations
 */

import React from 'react';
import { Spin, Card, Skeleton, Alert, Button, Space } from 'antd';
import { LoadingOutlined, ReloadOutlined } from '@ant-design/icons';

interface LoadingProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  className?: string;
}

/**
 * Generic Loading Spinner
 */
export function Loading({ size = 'default', tip = 'Loading...', className }: LoadingProps) {
  return (
    <div className={`flex items-center justify-center p-8 ${className || ''}`}>
      <Spin size={size} tip={tip} />
    </div>
  );
}

/**
 * Authentication Loading
 */
export function AuthLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <div className="text-center">
          <Spin size="large" />
          <div className="mt-4">
            <h3 className="text-lg font-medium">Authenticating...</h3>
            <p className="text-gray-500 mt-2">Please wait while we verify your credentials</p>
          </div>
        </div>
      </Card>
    </div>
  );
}

/**
 * Permission Check Loading
 */
export function PermissionLoading() {
  return (
    <div className="flex items-center justify-center p-4">
      <Space>
        <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} />
        <span className="text-gray-500">Checking permissions...</span>
      </Space>
    </div>
  );
}

/**
 * Data Loading Skeleton
 */
export function DataLoadingSkeleton({ 
  rows = 5, 
  showAvatar = false,
  showTitle = true 
}: { 
  rows?: number; 
  showAvatar?: boolean;
  showTitle?: boolean;
}) {
  return (
    <Card>
      {showTitle && <Skeleton.Input style={{ width: 200, height: 32 }} active />}
      <div className="mt-4">
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="mb-4">
            <Skeleton avatar={showAvatar} paragraph={{ rows: 1 }} active />
          </div>
        ))}
      </div>
    </Card>
  );
}

/**
 * Table Loading Skeleton
 */
export function TableLoadingSkeleton({ columns = 4, rows = 5 }: { columns?: number; rows?: number }) {
  return (
    <Card>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex space-x-4">
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton.Input key={index} style={{ width: 120, height: 32 }} active />
          ))}
        </div>
        
        {/* Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="flex space-x-4">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton.Input key={colIndex} style={{ width: 120, height: 24 }} active />
            ))}
          </div>
        ))}
      </div>
    </Card>
  );
}

/**
 * User Profile Loading
 */
export function UserProfileLoading() {
  return (
    <Card className="w-full max-w-md">
      <div className="flex items-center space-x-4 mb-4">
        <Skeleton.Avatar size={64} active />
        <div className="flex-1">
          <Skeleton.Input style={{ width: 150, height: 24 }} active />
          <div className="mt-2">
            <Skeleton.Input style={{ width: 200, height: 16 }} active />
          </div>
        </div>
      </div>
      
      <div className="space-y-3">
        <Skeleton.Input style={{ width: '100%', height: 20 }} active />
        <Skeleton.Input style={{ width: '80%', height: 20 }} active />
        <Skeleton.Input style={{ width: '60%', height: 20 }} active />
      </div>
    </Card>
  );
}

/**
 * Dashboard Loading
 */
export function DashboardLoading() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Skeleton.Input style={{ width: 300, height: 32 }} active />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <Skeleton.Input style={{ width: '100%', height: 60 }} active />
          </Card>
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <TableLoadingSkeleton />
        </div>
        <div>
          <UserProfileLoading />
        </div>
      </div>
    </div>
  );
}

/**
 * Retry Loading Component
 */
export function RetryLoading({ 
  error, 
  onRetry, 
  loading = false 
}: { 
  error?: string; 
  onRetry: () => void;
  loading?: boolean;
}) {
  return (
    <div className="flex items-center justify-center p-8">
      <Card className="w-full max-w-md">
        <div className="text-center">
          {loading ? (
            <>
              <Spin size="large" />
              <div className="mt-4">
                <h3 className="text-lg font-medium">Retrying...</h3>
                <p className="text-gray-500 mt-2">Please wait</p>
              </div>
            </>
          ) : (
            <>
              <Alert
                message="Loading Failed"
                description={error || 'Failed to load data'}
                type="error"
                showIcon
                className="mb-4"
              />
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={onRetry}
                size="large"
              >
                Try Again
              </Button>
            </>
          )}
        </div>
      </Card>
    </div>
  );
}

/**
 * Inline Loading Component
 */
export function InlineLoading({ 
  text = 'Loading...', 
  size = 'small' 
}: { 
  text?: string; 
  size?: 'small' | 'default' | 'large';
}) {
  return (
    <div className="flex items-center space-x-2 p-2">
      <Spin size={size} />
      <span className="text-gray-500">{text}</span>
    </div>
  );
}

/**
 * Full Page Loading
 */
export function FullPageLoading({ 
  title = 'Loading...', 
  subtitle 
}: { 
  title?: string; 
  subtitle?: string;
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Spin size="large" />
        <div className="mt-6">
          <h2 className="text-2xl font-semibold text-gray-900">{title}</h2>
          {subtitle && (
            <p className="text-gray-500 mt-2">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Content Loading Placeholder
 */
export function ContentLoadingPlaceholder() {
  return (
    <div className="space-y-6">
      <div>
        <Skeleton.Input style={{ width: 300, height: 32 }} active />
        <div className="mt-2">
          <Skeleton.Input style={{ width: 500, height: 16 }} active />
        </div>
      </div>
      
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index}>
            <Skeleton.Input style={{ width: 200, height: 24 }} active />
            <div className="mt-2 space-y-2">
              <Skeleton.Input style={{ width: '100%', height: 16 }} active />
              <Skeleton.Input style={{ width: '80%', height: 16 }} active />
              <Skeleton.Input style={{ width: '60%', height: 16 }} active />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
