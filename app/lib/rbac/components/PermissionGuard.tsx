/**
 * Permission Guard Components
 * Conditionally render content based on user permissions and roles
 */

import React, { type ReactNode } from "react";
import { useAuth, usePermissions } from "./AuthProvider";
import type { PermissionGuardProps, RoleGuardProps } from "../types";

/**
 * Permission Guard Component
 * Renders children only if user has required permissions
 */
export function PermissionGuard({
  permissions = [],
  roles = [],
  fallback = null,
  children,
  requireAll = true,
}: PermissionGuardProps) {
  const { hasPermission, hasRole, hasAnyRole, hasAllRoles } = usePermissions();
  const { isAuthenticated } = useAuth();

  // If not authenticated, don't render
  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  // Check permissions if provided
  if (permissions.length > 0) {
    const permissionResults = permissions.map((permission) => {
      if (permission.actions) {
        // Check if user has any of the required actions for the resource
        return permission.actions.some((action) =>
          hasPermission(permission.resource, action)
        );
      }
      return hasPermission(permission.resource, "read"); // Default to read permission
    });

    const hasRequiredPermissions = requireAll
      ? permissionResults.every((result) => result)
      : permissionResults.some((result) => result);

    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  // Check roles if provided
  if (roles.length > 0) {
    const hasRequiredRoles = requireAll
      ? hasAllRoles(roles)
      : hasAnyRole(roles);

    if (!hasRequiredRoles) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

/**
 * Role Guard Component
 * Renders children only if user has required roles
 */
export function RoleGuard({
  roles,
  fallback = null,
  children,
  requireAll = false,
}: RoleGuardProps) {
  const { hasRole, hasAnyRole, hasAllRoles, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  const hasRequiredRoles = requireAll ? hasAllRoles(roles) : hasAnyRole(roles);

  if (!hasRequiredRoles) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Admin Guard Component
 * Renders children only if user has admin or super admin role
 */
export function AdminGuard({
  fallback = null,
  children,
}: {
  fallback?: ReactNode;
  children: ReactNode;
}) {
  return (
    <RoleGuard roles={["admin", "super_admin"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

/**
 * Super Admin Guard Component
 * Renders children only if user has super admin role
 */
export function SuperAdminGuard({
  fallback = null,
  children,
}: {
  fallback?: ReactNode;
  children: ReactNode;
}) {
  return (
    <RoleGuard roles={["super_admin"]} requireAll={true} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

/**
 * Can Component
 * Renders children based on permission check
 */
export function Can({
  resource,
  action = "read",
  fallback = null,
  children,
}: {
  resource: string;
  action?: string;
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { hasPermission } = usePermissions();

  if (!hasPermission(resource, action)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Cannot Component
 * Renders children when user does NOT have permission (inverse of Can)
 */
export function Cannot({
  resource,
  action = "read",
  fallback = null,
  children,
}: {
  resource: string;
  action?: string;
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { hasPermission } = usePermissions();

  if (hasPermission(resource, action)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Authenticated Guard Component
 * Renders children only if user is authenticated
 */
export function AuthenticatedGuard({
  fallback = null,
  children,
}: {
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Unauthenticated Guard Component
 * Renders children only if user is NOT authenticated
 */
export function UnauthenticatedGuard({
  fallback = null,
  children,
}: {
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Resource Owner Guard Component
 * Renders children only if user owns the resource or has admin privileges
 */
export function ResourceOwnerGuard({
  resourceOwnerId,
  fallback = null,
  children,
}: {
  resourceOwnerId: string;
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { user, hasAnyRole } = useAuth();

  if (!user) {
    return <>{fallback}</>;
  }

  // Check if user is admin (can access any resource)
  const isAdmin = hasAnyRole(["admin", "super_admin"]);

  // Check if user owns the resource
  const isOwner = user.user.id === resourceOwnerId;

  if (!isAdmin && !isOwner) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Conditional Render Component
 * Renders different content based on multiple conditions
 */
export function ConditionalRender({
  conditions,
  children,
}: {
  conditions: {
    when: () => boolean;
    render: ReactNode;
  }[];
  children?: ReactNode; // Default fallback
}) {
  for (const condition of conditions) {
    if (condition.when()) {
      return <>{condition.render}</>;
    }
  }

  return <>{children}</>;
}

/**
 * Permission Switch Component
 * Renders different content based on permission levels
 */
export function PermissionSwitch({
  resource,
  cases,
  defaultCase = null,
}: {
  resource: string;
  cases: {
    action: string;
    render: ReactNode;
  }[];
  defaultCase?: ReactNode;
}) {
  const { hasPermission } = usePermissions();

  // Check cases in order and render the first match
  for (const caseItem of cases) {
    if (hasPermission(resource, caseItem.action)) {
      return <>{caseItem.render}</>;
    }
  }

  return <>{defaultCase}</>;
}

/**
 * Role Switch Component
 * Renders different content based on user roles
 */
export function RoleSwitch({
  cases,
  defaultCase = null,
}: {
  cases: {
    roles: string[];
    render: ReactNode;
    requireAll?: boolean;
  }[];
  defaultCase?: ReactNode;
}) {
  const { hasAnyRole, hasAllRoles } = useAuth();

  // Check cases in order and render the first match
  for (const caseItem of cases) {
    const hasRequiredRoles = caseItem.requireAll
      ? hasAllRoles(caseItem.roles)
      : hasAnyRole(caseItem.roles);

    if (hasRequiredRoles) {
      return <>{caseItem.render}</>;
    }
  }

  return <>{defaultCase}</>;
}
