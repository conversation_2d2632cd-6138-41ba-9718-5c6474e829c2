/**
 * RBAC Error Boundary Components
 * Handle authentication and authorization errors
 */

import React from 'react';
import { Result, <PERSON><PERSON>, <PERSON><PERSON>, Card, Typography } from 'antd';
import {
  ExceptionOutlined,
  LockOutlined,
  UserOutlined,
  HomeOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router';
import type { RBACError } from '../types';
import { REDIRECT_PATHS } from '../constants';

const { Title, Paragraph } = Typography;

interface ErrorBoundaryProps {
  error?: RBACError | Error;
  statusCode?: number;
  title?: string;
  subtitle?: string;
  showHomeButton?: boolean;
  showRetryButton?: boolean;
  onRetry?: () => void;
}

/**
 * Generic RBAC Error Display Component
 */
export function RBACErrorDisplay({
  error,
  statusCode = 500,
  title,
  subtitle,
  showHomeButton = true,
  showRetryButton = false,
  onRetry,
}: ErrorBoundaryProps) {
  const getErrorIcon = () => {
    if (statusCode === 401) return <UserOutlined />;
    if (statusCode === 403) return <LockOutlined />;
    return <ExceptionOutlined />;
  };

  const getErrorTitle = () => {
    if (title) return title;
    if (statusCode === 401) return 'Authentication Required';
    if (statusCode === 403) return 'Access Denied';
    return 'Something went wrong';
  };

  const getErrorSubtitle = () => {
    if (subtitle) return subtitle;
    if (statusCode === 401) return 'Please sign in to access this page';
    if (statusCode === 403) return 'You don\'t have permission to access this resource';
    return 'An unexpected error occurred';
  };

  const actions = [];

  if (statusCode === 401) {
    actions.push(
      <Link to={REDIRECT_PATHS.LOGIN} key="login">
        <Button type="primary" icon={<UserOutlined />}>
          Sign In
        </Button>
      </Link>
    );
  }

  if (showRetryButton && onRetry) {
    actions.push(
      <Button key="retry" icon={<ReloadOutlined />} onClick={onRetry}>
        Try Again
      </Button>
    );
  }

  if (showHomeButton) {
    actions.push(
      <Link to="/" key="home">
        <Button icon={<HomeOutlined />}>
          Go Home
        </Button>
      </Link>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="max-w-md w-full">
        <Result
          icon={getErrorIcon()}
          title={getErrorTitle()}
          subTitle={getErrorSubtitle()}
          extra={actions}
        />
        
        {error && (
          <Alert
            message="Error Details"
            description={error.message || 'Unknown error'}
            type="error"
            showIcon
            className="mt-4"
          />
        )}
      </Card>
    </div>
  );
}

/**
 * Unauthorized Access Component (401)
 */
export function UnauthorizedError({ onRetry }: { onRetry?: () => void }) {
  return (
    <RBACErrorDisplay
      statusCode={401}
      title="Authentication Required"
      subtitle="Please sign in to access this page"
      showRetryButton={!!onRetry}
      onRetry={onRetry}
    />
  );
}

/**
 * Forbidden Access Component (403)
 */
export function ForbiddenError({ onRetry }: { onRetry?: () => void }) {
  return (
    <RBACErrorDisplay
      statusCode={403}
      title="Access Denied"
      subtitle="You don't have permission to access this resource"
      showRetryButton={!!onRetry}
      onRetry={onRetry}
    />
  );
}

/**
 * Loading Error Component
 */
export function LoadingError({ 
  error, 
  onRetry 
}: { 
  error?: Error; 
  onRetry?: () => void; 
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="max-w-md w-full">
        <Result
          status="error"
          title="Loading Failed"
          subTitle="Failed to load the requested data"
          extra={[
            <Button key="retry" type="primary" icon={<ReloadOutlined />} onClick={onRetry}>
              Try Again
            </Button>,
            <Link to="/" key="home">
              <Button icon={<HomeOutlined />}>
                Go Home
              </Button>
            </Link>,
          ]}
        />
        
        {error && (
          <Alert
            message="Error Details"
            description={error.message}
            type="error"
            showIcon
            className="mt-4"
          />
        )}
      </Card>
    </div>
  );
}

/**
 * Permission Denied Component (for UI elements)
 */
export function PermissionDenied({ 
  resource, 
  action,
  message 
}: { 
  resource?: string; 
  action?: string;
  message?: string;
}) {
  const defaultMessage = resource && action 
    ? `You don't have permission to ${action} ${resource}`
    : 'You don\'t have permission to view this content';

  return (
    <Alert
      message="Permission Denied"
      description={message || defaultMessage}
      type="warning"
      showIcon
      icon={<LockOutlined />}
      className="m-4"
    />
  );
}

/**
 * Role Required Component
 */
export function RoleRequired({ 
  requiredRoles, 
  currentRole 
}: { 
  requiredRoles: string[]; 
  currentRole?: string;
}) {
  return (
    <Alert
      message="Insufficient Role"
      description={
        <div>
          <Paragraph>
            This feature requires one of the following roles: {requiredRoles.join(', ')}
          </Paragraph>
          {currentRole && (
            <Paragraph type="secondary">
              Your current role: {currentRole}
            </Paragraph>
          )}
        </div>
      }
      type="warning"
      showIcon
      icon={<LockOutlined />}
      className="m-4"
    />
  );
}

/**
 * Tenant Access Denied Component
 */
export function TenantAccessDenied({ 
  tenantName 
}: { 
  tenantName?: string;
}) {
  return (
    <Alert
      message="Tenant Access Denied"
      description={
        tenantName 
          ? `You don't have access to ${tenantName}`
          : 'You don\'t have access to this organization'
      }
      type="error"
      showIcon
      icon={<LockOutlined />}
      className="m-4"
    />
  );
}

/**
 * Session Expired Component
 */
export function SessionExpired() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="max-w-md w-full">
        <Result
          status="warning"
          title="Session Expired"
          subTitle="Your session has expired. Please sign in again."
          extra={[
            <Link to={REDIRECT_PATHS.LOGIN} key="login">
              <Button type="primary" icon={<UserOutlined />}>
                Sign In Again
              </Button>
            </Link>,
          ]}
        />
      </Card>
    </div>
  );
}
