/**
 * Advanced Role Manager
 * Comprehensive role-based access control with dynamic permissions
 */

import { secureStorage } from '../../../utils/secureStorage';
import { loggerHelpers } from '../../../store/middleware/loggingMiddleware';

interface RoleDefinition {
  id: string;
  name: string;
  displayName: string;
  description: string;
  level: number;
  permissions: string[];
  inheritsFrom: string[];
  isSystemRole: boolean;
  conditions?: RoleCondition[];
}

interface RoleCondition {
  type: 'time' | 'location' | 'device' | 'custom';
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte';
  value: any;
  field?: string;
}

interface PermissionDefinition {
  id: string;
  name: string;
  resource: string;
  actions: PermissionAction[];
  conditions?: PermissionCondition[];
  scope: 'global' | 'tenant' | 'user';
}

class RoleManager {
  private roles: Map<string, RoleDefinition> = new Map();
  private permissions: Map<string, PermissionDefinition> = new Map();
  private roleHierarchy: Map<string, string[]> = new Map();

  constructor() {
    this.initializeSystemRoles();
    this.initializeSystemPermissions();
    this.buildRoleHierarchy();
  }

  /**
   * Initialize system roles
   */
  private initializeSystemRoles() {
    const systemRoles: RoleDefinition[] = [
      {
        id: 'super_admin',
        name: 'super_admin',
        displayName: 'Super Administrator',
        description: 'Full system access with all permissions',
        level: 1,
        permissions: ['*'],
        inheritsFrom: [],
        isSystemRole: true,
      },
      {
        id: 'admin',
        name: 'admin',
        displayName: 'Administrator',
        description: 'Administrative access to manage users and settings',
        level: 2,
        permissions: [
          'users.manage',
          'roles.manage',
          'settings.manage',
          'audit.read',
          'content.manage',
          'translations.manage',
        ],
        inheritsFrom: ['moderator'],
        isSystemRole: true,
      },
      {
        id: 'moderator',
        name: 'moderator',
        displayName: 'Moderator',
        description: 'Content moderation and user management',
        level: 3,
        permissions: [
          'users.read',
          'users.update',
          'content.manage',
          'reports.read',
        ],
        inheritsFrom: ['editor'],
        isSystemRole: true,
      },
      {
        id: 'editor',
        name: 'editor',
        displayName: 'Editor',
        description: 'Content creation and editing',
        level: 4,
        permissions: [
          'content.create',
          'content.update',
          'content.read',
          'translations.update',
          'files.upload',
        ],
        inheritsFrom: ['user'],
        isSystemRole: true,
      },
      {
        id: 'user',
        name: 'user',
        displayName: 'User',
        description: 'Basic user access',
        level: 5,
        permissions: [
          'profile.read',
          'profile.update',
          'content.read',
        ],
        inheritsFrom: [],
        isSystemRole: true,
      },
      {
        id: 'viewer',
        name: 'viewer',
        displayName: 'Viewer',
        description: 'Read-only access',
        level: 6,
        permissions: [
          'content.read',
          'profile.read',
        ],
        inheritsFrom: [],
        isSystemRole: true,
      },
    ];

    systemRoles.forEach(role => {
      this.roles.set(role.id, role);
    });
  }

  /**
   * Initialize system permissions
   */
  private initializeSystemPermissions() {
    const systemPermissions: PermissionDefinition[] = [
      // User permissions
      {
        id: 'users.create',
        name: 'Create Users',
        resource: 'users',
        actions: ['create'],
        scope: 'tenant',
      },
      {
        id: 'users.read',
        name: 'Read Users',
        resource: 'users',
        actions: ['read'],
        scope: 'tenant',
      },
      {
        id: 'users.update',
        name: 'Update Users',
        resource: 'users',
        actions: ['update'],
        scope: 'tenant',
      },
      {
        id: 'users.delete',
        name: 'Delete Users',
        resource: 'users',
        actions: ['delete'],
        scope: 'tenant',
      },
      {
        id: 'users.manage',
        name: 'Manage Users',
        resource: 'users',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        scope: 'tenant',
      },

      // Role permissions
      {
        id: 'roles.manage',
        name: 'Manage Roles',
        resource: 'roles',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        scope: 'global',
      },

      // Content permissions
      {
        id: 'content.create',
        name: 'Create Content',
        resource: 'content',
        actions: ['create'],
        scope: 'tenant',
      },
      {
        id: 'content.read',
        name: 'Read Content',
        resource: 'content',
        actions: ['read'],
        scope: 'tenant',
      },
      {
        id: 'content.update',
        name: 'Update Content',
        resource: 'content',
        actions: ['update'],
        scope: 'tenant',
      },
      {
        id: 'content.delete',
        name: 'Delete Content',
        resource: 'content',
        actions: ['delete'],
        scope: 'tenant',
      },
      {
        id: 'content.manage',
        name: 'Manage Content',
        resource: 'content',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        scope: 'tenant',
      },

      // Settings permissions
      {
        id: 'settings.read',
        name: 'Read Settings',
        resource: 'settings',
        actions: ['read'],
        scope: 'global',
      },
      {
        id: 'settings.update',
        name: 'Update Settings',
        resource: 'settings',
        actions: ['update'],
        scope: 'global',
      },
      {
        id: 'settings.manage',
        name: 'Manage Settings',
        resource: 'settings',
        actions: ['read', 'update', 'manage'],
        scope: 'global',
      },

      // Audit permissions
      {
        id: 'audit.read',
        name: 'Read Audit Logs',
        resource: 'audit',
        actions: ['read'],
        scope: 'global',
      },

      // Translation permissions
      {
        id: 'translations.read',
        name: 'Read Translations',
        resource: 'translations',
        actions: ['read'],
        scope: 'tenant',
      },
      {
        id: 'translations.update',
        name: 'Update Translations',
        resource: 'translations',
        actions: ['update'],
        scope: 'tenant',
      },
      {
        id: 'translations.manage',
        name: 'Manage Translations',
        resource: 'translations',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        scope: 'tenant',
      },

      // File permissions
      {
        id: 'files.upload',
        name: 'Upload Files',
        resource: 'files',
        actions: ['create'],
        scope: 'tenant',
      },
      {
        id: 'files.manage',
        name: 'Manage Files',
        resource: 'files',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        scope: 'tenant',
      },

      // Profile permissions
      {
        id: 'profile.read',
        name: 'Read Profile',
        resource: 'profile',
        actions: ['read'],
        scope: 'user',
      },
      {
        id: 'profile.update',
        name: 'Update Profile',
        resource: 'profile',
        actions: ['update'],
        scope: 'user',
      },

      // Reports permissions
      {
        id: 'reports.read',
        name: 'Read Reports',
        resource: 'reports',
        actions: ['read'],
        scope: 'tenant',
      },
    ];

    systemPermissions.forEach(permission => {
      this.permissions.set(permission.id, permission);
    });
  }

  /**
   * Build role hierarchy for inheritance
   */
  private buildRoleHierarchy() {
    this.roles.forEach(role => {
      const inherited = this.getInheritedPermissions(role.id);
      this.roleHierarchy.set(role.id, inherited);
    });
  }

  /**
   * Get all permissions for a role including inherited ones
   */
  getInheritedPermissions(roleId: string): string[] {
    const role = this.roles.get(roleId);
    if (!role) return [];

    const allPermissions = new Set<string>(role.permissions);

    // Add permissions from inherited roles
    role.inheritsFrom.forEach(inheritedRoleId => {
      const inheritedPermissions = this.getInheritedPermissions(inheritedRoleId);
      inheritedPermissions.forEach(permission => allPermissions.add(permission));
    });

    return Array.from(allPermissions);
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(
    userRoles: string[],
    resource: string,
    action: PermissionAction,
    context?: {
      userId?: string;
      tenantId?: string;
      resourceId?: string;
      conditions?: Record<string, any>;
    }
  ): { allowed: boolean; reason?: string } {
    // Super admin has all permissions
    if (userRoles.includes('super_admin')) {
      return { allowed: true };
    }

    // Get all permissions for user's roles
    const userPermissions = new Set<string>();
    userRoles.forEach(roleId => {
      const rolePermissions = this.getInheritedPermissions(roleId);
      rolePermissions.forEach(permission => userPermissions.add(permission));
    });

    // Check for wildcard permission
    if (userPermissions.has('*')) {
      return { allowed: true };
    }

    // Check for specific resource permissions
    const resourcePermissions = [
      `${resource}.${action}`,
      `${resource}.manage`,
      `${resource}.*`,
    ];

    const hasResourcePermission = resourcePermissions.some(permission =>
      userPermissions.has(permission)
    );

    if (!hasResourcePermission) {
      return { allowed: false, reason: `Missing permission: ${resource}.${action}` };
    }

    // Check conditions if provided
    if (context?.conditions) {
      const conditionResult = this.checkConditions(userRoles, context.conditions);
      if (!conditionResult.allowed) {
        return conditionResult;
      }
    }

    loggerHelpers.debug('Permission check passed', {
      userRoles,
      resource,
      action,
      context,
    });

    return { allowed: true };
  }

  /**
   * Check role conditions
   */
  private checkConditions(
    userRoles: string[],
    conditions: Record<string, any>
  ): { allowed: boolean; reason?: string } {
    // Get all role conditions
    const roleConditions: RoleCondition[] = [];
    userRoles.forEach(roleId => {
      const role = this.roles.get(roleId);
      if (role?.conditions) {
        roleConditions.push(...role.conditions);
      }
    });

    // Check each condition
    for (const condition of roleConditions) {
      const result = this.evaluateCondition(condition, conditions);
      if (!result) {
        return {
          allowed: false,
          reason: `Role condition failed: ${condition.type}`,
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(condition: RoleCondition, context: Record<string, any>): boolean {
    const contextValue = condition.field ? context[condition.field] : context[condition.type];
    const conditionValue = condition.value;

    switch (condition.operator) {
      case 'eq':
        return contextValue === conditionValue;
      case 'ne':
        return contextValue !== conditionValue;
      case 'in':
        return Array.isArray(conditionValue) && conditionValue.includes(contextValue);
      case 'nin':
        return Array.isArray(conditionValue) && !conditionValue.includes(contextValue);
      case 'gt':
        return contextValue > conditionValue;
      case 'gte':
        return contextValue >= conditionValue;
      case 'lt':
        return contextValue < conditionValue;
      case 'lte':
        return contextValue <= conditionValue;
      default:
        return false;
    }
  }

  /**
   * Get role by ID
   */
  getRole(roleId: string): RoleDefinition | undefined {
    return this.roles.get(roleId);
  }

  /**
   * Get all roles
   */
  getAllRoles(): RoleDefinition[] {
    return Array.from(this.roles.values());
  }

  /**
   * Get system roles only
   */
  getSystemRoles(): RoleDefinition[] {
    return Array.from(this.roles.values()).filter(role => role.isSystemRole);
  }

  /**
   * Get permission by ID
   */
  getPermission(permissionId: string): PermissionDefinition | undefined {
    return this.permissions.get(permissionId);
  }

  /**
   * Get all permissions
   */
  getAllPermissions(): PermissionDefinition[] {
    return Array.from(this.permissions.values());
  }

  /**
   * Add custom role
   */
  addRole(role: Omit<RoleDefinition, 'isSystemRole'>): void {
    const customRole: RoleDefinition = {
      ...role,
      isSystemRole: false,
    };
    
    this.roles.set(role.id, customRole);
    this.buildRoleHierarchy();
    
    loggerHelpers.info('Custom role added', { roleId: role.id });
  }

  /**
   * Remove custom role
   */
  removeRole(roleId: string): boolean {
    const role = this.roles.get(roleId);
    if (!role || role.isSystemRole) {
      return false;
    }

    this.roles.delete(roleId);
    this.roleHierarchy.delete(roleId);
    this.buildRoleHierarchy();
    
    loggerHelpers.info('Custom role removed', { roleId });
    return true;
  }

  /**
   * Get role hierarchy
   */
  getRoleHierarchy(): Map<string, string[]> {
    return new Map(this.roleHierarchy);
  }

  /**
   * Check if role exists
   */
  roleExists(roleId: string): boolean {
    return this.roles.has(roleId);
  }

  /**
   * Get effective role for user (highest priority role)
   */
  getEffectiveRole(userRoles: string[]): RoleDefinition | null {
    if (userRoles.length === 0) return null;

    // Sort roles by level (lower level = higher priority)
    const sortedRoles = userRoles
      .map(roleId => this.roles.get(roleId))
      .filter(Boolean)
      .sort((a, b) => a!.level - b!.level);

    return sortedRoles[0] || null;
  }
}

// Export singleton instance
export const roleManager = new RoleManager();
export default roleManager;
