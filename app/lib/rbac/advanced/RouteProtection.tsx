/**
 * Advanced Route Protection System
 * Dynamic route guards with role-based access control
 */

import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router';
import { useSelector } from 'react-redux';
import type { RootState } from '../../../store';
import { roleManager } from './RoleManager';
import { APP_CONFIG } from '../../../config/app';
import { loggerHelpers } from '../../../store/middleware/loggingMiddleware';
import { LoadingSpinner } from '../../components/LoadingStates';
import { ForbiddenError, UnauthorizedError } from '../../components/ErrorBoundary';

interface RouteProtectionProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredPermissions?: Array<{
    resource: string;
    action: PermissionAction;
  }>;
  requireAll?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
  conditions?: Record<string, any>;
}

interface RouteGuardConfig {
  path: string;
  roles?: UserRole[];
  permissions?: Array<{
    resource: string;
    action: PermissionAction;
  }>;
  requireAll?: boolean;
  conditions?: Record<string, any>;
  redirectTo?: string;
}

/**
 * Main Route Protection Component
 */
export function RouteProtection({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAll = false,
  fallback,
  redirectTo,
  conditions = {},
}: RouteProtectionProps) {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);
  const [accessCheck, setAccessCheck] = useState<{
    loading: boolean;
    allowed: boolean;
    reason?: string;
  }>({ loading: true, allowed: false });

  useEffect(() => {
    checkAccess();
  }, [user, isAuthenticated, location.pathname]);

  const checkAccess = async () => {
    setAccessCheck({ loading: true, allowed: false });

    // Not authenticated
    if (!isAuthenticated || !user) {
      setAccessCheck({
        loading: false,
        allowed: false,
        reason: 'Not authenticated',
      });
      return;
    }

    // Check roles if required
    if (requiredRoles.length > 0) {
      const userRoles = [user.role];
      const hasRequiredRoles = requireAll
        ? requiredRoles.every(role => userRoles.includes(role))
        : requiredRoles.some(role => userRoles.includes(role));

      if (!hasRequiredRoles) {
        setAccessCheck({
          loading: false,
          allowed: false,
          reason: `Missing required role(s): ${requiredRoles.join(', ')}`,
        });
        return;
      }
    }

    // Check permissions if required
    if (requiredPermissions.length > 0) {
      const userRoles = [user.role];
      const permissionChecks = requiredPermissions.map(({ resource, action }) =>
        roleManager.hasPermission(userRoles, resource, action, {
          userId: user.id,
          conditions,
        })
      );

      const hasRequiredPermissions = requireAll
        ? permissionChecks.every(check => check.allowed)
        : permissionChecks.some(check => check.allowed);

      if (!hasRequiredPermissions) {
        const failedCheck = permissionChecks.find(check => !check.allowed);
        setAccessCheck({
          loading: false,
          allowed: false,
          reason: failedCheck?.reason || 'Insufficient permissions',
        });
        return;
      }
    }

    // Log successful access
    loggerHelpers.info('Route access granted', {
      path: location.pathname,
      userId: user.id,
      userRole: user.role,
      requiredRoles,
      requiredPermissions,
    });

    setAccessCheck({ loading: false, allowed: true });
  };

  // Still loading auth state
  if (isLoading || accessCheck.loading) {
    return <LoadingSpinner />;
  }

  // Not authenticated - redirect to login
  if (!isAuthenticated) {
    const loginPath = APP_CONFIG.routes.login;
    const returnUrl = encodeURIComponent(location.pathname + location.search);
    return <Navigate to={`${loginPath}?returnUrl=${returnUrl}`} replace />;
  }

  // Access denied
  if (!accessCheck.allowed) {
    // Log access denial
    loggerHelpers.warn('Route access denied', {
      path: location.pathname,
      userId: user?.id,
      userRole: user?.role,
      reason: accessCheck.reason,
      requiredRoles,
      requiredPermissions,
    });

    // Custom redirect
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }

    // Custom fallback
    if (fallback) {
      return <>{fallback}</>;
    }

    // Default error pages
    if (accessCheck.reason === 'Not authenticated') {
      return <UnauthorizedError />;
    }

    return <ForbiddenError />;
  }

  // Access granted
  return <>{children}</>;
}

/**
 * Higher-order component for route protection
 */
export function withRouteProtection<P extends object>(
  Component: React.ComponentType<P>,
  protection: Omit<RouteProtectionProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <RouteProtection {...protection}>
        <Component {...props} />
      </RouteProtection>
    );
  };
}

/**
 * Hook for checking route access
 */
export function useRouteAccess(
  requiredRoles: UserRole[] = [],
  requiredPermissions: Array<{
    resource: string;
    action: PermissionAction;
  }> = [],
  requireAll = false
) {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const [access, setAccess] = useState<{
    allowed: boolean;
    reason?: string;
  }>({ allowed: false });

  useEffect(() => {
    if (!isAuthenticated || !user) {
      setAccess({ allowed: false, reason: 'Not authenticated' });
      return;
    }

    // Check roles
    if (requiredRoles.length > 0) {
      const userRoles = [user.role];
      const hasRequiredRoles = requireAll
        ? requiredRoles.every(role => userRoles.includes(role))
        : requiredRoles.some(role => userRoles.includes(role));

      if (!hasRequiredRoles) {
        setAccess({
          allowed: false,
          reason: `Missing required role(s): ${requiredRoles.join(', ')}`,
        });
        return;
      }
    }

    // Check permissions
    if (requiredPermissions.length > 0) {
      const userRoles = [user.role];
      const permissionChecks = requiredPermissions.map(({ resource, action }) =>
        roleManager.hasPermission(userRoles, resource, action, {
          userId: user.id,
        })
      );

      const hasRequiredPermissions = requireAll
        ? permissionChecks.every(check => check.allowed)
        : permissionChecks.some(check => check.allowed);

      if (!hasRequiredPermissions) {
        const failedCheck = permissionChecks.find(check => !check.allowed);
        setAccess({
          allowed: false,
          reason: failedCheck?.reason || 'Insufficient permissions',
        });
        return;
      }
    }

    setAccess({ allowed: true });
  }, [user, isAuthenticated, requiredRoles, requiredPermissions, requireAll]);

  return access;
}

/**
 * Route Guard Configuration Manager
 */
class RouteGuardManager {
  private guards: Map<string, RouteGuardConfig> = new Map();

  /**
   * Register route guard
   */
  registerGuard(config: RouteGuardConfig) {
    this.guards.set(config.path, config);
    loggerHelpers.debug('Route guard registered', { path: config.path });
  }

  /**
   * Get guard for path
   */
  getGuard(path: string): RouteGuardConfig | undefined {
    // Exact match first
    if (this.guards.has(path)) {
      return this.guards.get(path);
    }

    // Pattern matching
    for (const [guardPath, config] of this.guards.entries()) {
      if (this.matchPath(guardPath, path)) {
        return config;
      }
    }

    return undefined;
  }

  /**
   * Check if path matches pattern
   */
  private matchPath(pattern: string, path: string): boolean {
    // Convert pattern to regex
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/:\w+/g, '[^/]+');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(path);
  }

  /**
   * Remove guard
   */
  removeGuard(path: string) {
    this.guards.delete(path);
    loggerHelpers.debug('Route guard removed', { path });
  }

  /**
   * Get all guards
   */
  getAllGuards(): RouteGuardConfig[] {
    return Array.from(this.guards.values());
  }

  /**
   * Clear all guards
   */
  clearGuards() {
    this.guards.clear();
    loggerHelpers.debug('All route guards cleared');
  }
}

// Export singleton instance
export const routeGuardManager = new RouteGuardManager();

// Initialize default route guards
routeGuardManager.registerGuard({
  path: '/admin/*',
  roles: ['admin', 'super_admin'],
  requireAll: false,
});

routeGuardManager.registerGuard({
  path: '/sa/*',
  roles: ['super_admin'],
  requireAll: true,
});

routeGuardManager.registerGuard({
  path: '/admin/users',
  permissions: [
    { resource: 'users', action: 'read' },
  ],
});

routeGuardManager.registerGuard({
  path: '/admin/settings',
  permissions: [
    { resource: 'settings', action: 'read' },
  ],
});

routeGuardManager.registerGuard({
  path: '/admin/audit',
  permissions: [
    { resource: 'audit', action: 'read' },
  ],
});

/**
 * Dynamic Route Guard Component
 */
export function DynamicRouteGuard({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const guard = routeGuardManager.getGuard(location.pathname);

  if (!guard) {
    return <>{children}</>;
  }

  return (
    <RouteProtection
      requiredRoles={guard.roles}
      requiredPermissions={guard.permissions}
      requireAll={guard.requireAll}
      conditions={guard.conditions}
      redirectTo={guard.redirectTo}
    >
      {children}
    </RouteProtection>
  );
}
