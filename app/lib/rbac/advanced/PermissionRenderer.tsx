/**
 * Permission-Based UI Rendering
 * Components for conditional rendering based on user permissions
 */

import React from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../../store';
import { roleManager } from './RoleManager';
import { loggerHelpers } from '../../../store/middleware/loggingMiddleware';

interface PermissionRendererProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
}

interface HasPermissionProps extends PermissionRendererProps {
  resource: string;
  action: PermissionAction;
  conditions?: Record<string, any>;
}

interface HasRoleProps extends PermissionRendererProps {
  roles: UserRole | UserRole[];
  requireAll?: boolean;
}

interface HasAnyPermissionProps extends PermissionRendererProps {
  permissions: Array<{
    resource: string;
    action: PermissionAction;
  }>;
  conditions?: Record<string, any>;
}

/**
 * Render children if user has specific permission
 */
export function HasPermission({
  children,
  resource,
  action,
  conditions = {},
  fallback = null,
  loading = null,
}: HasPermissionProps) {
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  const userRoles = [user.role];
  const permissionCheck = roleManager.hasPermission(userRoles, resource, action, {
    userId: user.id,
    conditions,
  });

  if (!permissionCheck.allowed) {
    loggerHelpers.debug('Permission denied for UI element', {
      resource,
      action,
      reason: permissionCheck.reason,
      userId: user.id,
    });
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Render children if user has specific role(s)
 */
export function HasRole({
  children,
  roles,
  requireAll = false,
  fallback = null,
  loading = null,
}: HasRoleProps) {
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  const requiredRoles = Array.isArray(roles) ? roles : [roles];
  const userRoles = [user.role];

  const hasRequiredRoles = requireAll
    ? requiredRoles.every(role => userRoles.includes(role))
    : requiredRoles.some(role => userRoles.includes(role));

  if (!hasRequiredRoles) {
    loggerHelpers.debug('Role check failed for UI element', {
      requiredRoles,
      userRoles,
      requireAll,
      userId: user.id,
    });
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Render children if user has any of the specified permissions
 */
export function HasAnyPermission({
  children,
  permissions,
  conditions = {},
  fallback = null,
  loading = null,
}: HasAnyPermissionProps) {
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  const userRoles = [user.role];
  const hasAnyPermission = permissions.some(({ resource, action }) => {
    const permissionCheck = roleManager.hasPermission(userRoles, resource, action, {
      userId: user.id,
      conditions,
    });
    return permissionCheck.allowed;
  });

  if (!hasAnyPermission) {
    loggerHelpers.debug('No permissions found for UI element', {
      permissions,
      userId: user.id,
    });
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Render children if user is authenticated
 */
export function IsAuthenticated({
  children,
  fallback = null,
  loading = null,
}: PermissionRendererProps) {
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Render children if user is NOT authenticated
 */
export function IsNotAuthenticated({
  children,
  fallback = null,
  loading = null,
}: PermissionRendererProps) {
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Render children if user is the owner of the resource
 */
export function IsOwner({
  children,
  resourceOwnerId,
  fallback = null,
  loading = null,
}: PermissionRendererProps & {
  resourceOwnerId: string;
}) {
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  if (user.id !== resourceOwnerId) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Conditional renderer based on multiple criteria
 */
export function ConditionalRender({
  children,
  condition,
  fallback = null,
  loading = null,
}: PermissionRendererProps & {
  condition: () => boolean;
}) {
  const { isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  try {
    const shouldRender = condition();
    return shouldRender ? <>{children}</> : <>{fallback}</>;
  } catch (error) {
    loggerHelpers.error('Conditional render error', error);
    return <>{fallback}</>;
  }
}

/**
 * Permission-based switch component
 */
interface PermissionSwitchCase {
  permissions?: Array<{
    resource: string;
    action: PermissionAction;
  }>;
  roles?: UserRole[];
  condition?: () => boolean;
  component: React.ReactNode;
}

interface PermissionSwitchProps {
  cases: PermissionSwitchCase[];
  defaultCase?: React.ReactNode;
  loading?: React.ReactNode;
}

export function PermissionSwitch({
  cases,
  defaultCase = null,
  loading = null,
}: PermissionSwitchProps) {
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  if (isLoading) {
    return <>{loading}</>;
  }

  if (!isAuthenticated || !user) {
    return <>{defaultCase}</>;
  }

  const userRoles = [user.role];

  for (const switchCase of cases) {
    let shouldRender = true;

    // Check permissions
    if (switchCase.permissions) {
      shouldRender = switchCase.permissions.every(({ resource, action }) => {
        const permissionCheck = roleManager.hasPermission(userRoles, resource, action, {
          userId: user.id,
        });
        return permissionCheck.allowed;
      });
    }

    // Check roles
    if (shouldRender && switchCase.roles) {
      shouldRender = switchCase.roles.some(role => userRoles.includes(role));
    }

    // Check custom condition
    if (shouldRender && switchCase.condition) {
      try {
        shouldRender = switchCase.condition();
      } catch (error) {
        loggerHelpers.error('Permission switch condition error', error);
        shouldRender = false;
      }
    }

    if (shouldRender) {
      return <>{switchCase.component}</>;
    }
  }

  return <>{defaultCase}</>;
}

/**
 * Hook for checking permissions in components
 */
export function usePermissions() {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);

  const hasPermission = (
    resource: string,
    action: PermissionAction,
    conditions: Record<string, any> = {}
  ): boolean => {
    if (!isAuthenticated || !user) return false;

    const userRoles = [user.role];
    const permissionCheck = roleManager.hasPermission(userRoles, resource, action, {
      userId: user.id,
      conditions,
    });

    return permissionCheck.allowed;
  };

  const hasRole = (roles: UserRole | UserRole[], requireAll = false): boolean => {
    if (!isAuthenticated || !user) return false;

    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    const userRoles = [user.role];

    return requireAll
      ? requiredRoles.every(role => userRoles.includes(role))
      : requiredRoles.some(role => userRoles.includes(role));
  };

  const hasAnyPermission = (
    permissions: Array<{
      resource: string;
      action: PermissionAction;
    }>,
    conditions: Record<string, any> = {}
  ): boolean => {
    if (!isAuthenticated || !user) return false;

    const userRoles = [user.role];
    return permissions.some(({ resource, action }) => {
      const permissionCheck = roleManager.hasPermission(userRoles, resource, action, {
        userId: user.id,
        conditions,
      });
      return permissionCheck.allowed;
    });
  };

  const isOwner = (resourceOwnerId: string): boolean => {
    return isAuthenticated && user?.id === resourceOwnerId;
  };

  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    isOwner,
    user,
    isAuthenticated,
  };
}
