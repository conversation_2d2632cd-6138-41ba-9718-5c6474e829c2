/**
 * Advanced Audit Logging System
 * Comprehensive audit trail for security and compliance
 */

import { secureStorage } from '../../../utils/secureStorage';
import { SECURITY_CONFIG } from '../../../config/security';
import { loggerHelpers } from '../../../store/middleware/loggingMiddleware';

interface AuditContext {
  userId?: string;
  sessionId?: string;
  ip?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    coordinates?: [number, number];
  };
  device?: {
    type: 'desktop' | 'mobile' | 'tablet';
    os?: string;
    browser?: string;
  };
}

interface AuditMetadata {
  duration?: number;
  requestId?: string;
  correlationId?: string;
  tags?: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'security' | 'data' | 'system' | 'user' | 'admin';
}

interface DetailedAuditLog extends AuditLog {
  context: AuditContext;
  metadata: AuditMetadata;
  stackTrace?: string;
  relatedEvents?: string[];
}

class AuditLogger {
  private logs: DetailedAuditLog[] = [];
  private maxLogs = 10000;
  private batchSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startPeriodicFlush();
    this.setupUnloadHandler();
  }

  /**
   * Log an audit event
   */
  log(
    action: string,
    resource: string,
    details: {
      resourceId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      success?: boolean;
      error?: string;
      context?: Partial<AuditContext>;
      metadata?: Partial<AuditMetadata>;
    } = {}
  ): string {
    const auditId = this.generateAuditId();
    const timestamp = new Date().toISOString();
    const context = this.buildContext(details.context);
    const metadata = this.buildMetadata(details.metadata);

    const auditLog: DetailedAuditLog = {
      id: auditId,
      userId: context.userId || 'anonymous',
      action,
      resource,
      resourceId: details.resourceId,
      oldValues: this.sanitizeValues(details.oldValues),
      newValues: this.sanitizeValues(details.newValues),
      ip: context.ip || 'unknown',
      userAgent: context.userAgent || 'unknown',
      timestamp,
      success: details.success ?? true,
      error: details.error,
      context,
      metadata,
    };

    // Add stack trace for errors
    if (!details.success && details.error) {
      auditLog.stackTrace = new Error().stack;
    }

    this.addLog(auditLog);
    this.logToConsole(auditLog);

    return auditId;
  }

  /**
   * Log security event
   */
  logSecurity(
    type: SecurityEventType,
    details: {
      description?: string;
      severity?: SecurityEvent['severity'];
      context?: Partial<AuditContext>;
      metadata?: Record<string, any>;
    } = {}
  ): string {
    return this.log('security_event', 'security', {
      resourceId: type,
      newValues: {
        type,
        description: details.description,
        metadata: details.metadata,
      },
      success: true,
      context: details.context,
      metadata: {
        severity: details.severity || 'medium',
        category: 'security',
      },
    });
  }

  /**
   * Log data access
   */
  logDataAccess(
    resource: string,
    action: 'read' | 'export' | 'search',
    details: {
      resourceId?: string;
      query?: string;
      filters?: Record<string, any>;
      resultCount?: number;
      context?: Partial<AuditContext>;
    } = {}
  ): string {
    return this.log(`data_${action}`, resource, {
      resourceId: details.resourceId,
      newValues: {
        query: details.query,
        filters: details.filters,
        resultCount: details.resultCount,
      },
      success: true,
      context: details.context,
      metadata: {
        severity: 'low',
        category: 'data',
      },
    });
  }

  /**
   * Log administrative action
   */
  logAdminAction(
    action: string,
    resource: string,
    details: {
      resourceId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      reason?: string;
      context?: Partial<AuditContext>;
    } = {}
  ): string {
    return this.log(action, resource, {
      ...details,
      newValues: {
        ...details.newValues,
        reason: details.reason,
      },
      metadata: {
        severity: 'high',
        category: 'admin',
      },
    });
  }

  /**
   * Log user action
   */
  logUserAction(
    action: string,
    resource: string,
    details: {
      resourceId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      context?: Partial<AuditContext>;
    } = {}
  ): string {
    return this.log(action, resource, {
      ...details,
      metadata: {
        severity: 'low',
        category: 'user',
      },
    });
  }

  /**
   * Log system event
   */
  logSystemEvent(
    event: string,
    details: {
      description?: string;
      metadata?: Record<string, any>;
      error?: string;
      success?: boolean;
    } = {}
  ): string {
    return this.log(event, 'system', {
      newValues: {
        description: details.description,
        metadata: details.metadata,
      },
      error: details.error,
      success: details.success ?? true,
      metadata: {
        severity: details.error ? 'high' : 'low',
        category: 'system',
      },
    });
  }

  /**
   * Get audit logs with filtering
   */
  getLogs(filters: {
    userId?: string;
    resource?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    severity?: AuditMetadata['severity'];
    category?: AuditMetadata['category'];
    limit?: number;
  } = {}): DetailedAuditLog[] {
    let filteredLogs = [...this.logs];

    // Apply filters
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.resource) {
      filteredLogs = filteredLogs.filter(log => log.resource === filters.resource);
    }

    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }

    if (filters.startDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startDate!);
    }

    if (filters.endDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endDate!);
    }

    if (filters.severity) {
      filteredLogs = filteredLogs.filter(log => log.metadata.severity === filters.severity);
    }

    if (filters.category) {
      filteredLogs = filteredLogs.filter(log => log.metadata.category === filters.category);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Apply limit
    if (filters.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  /**
   * Export audit logs
   */
  exportLogs(
    format: 'json' | 'csv' | 'xml',
    filters: Parameters<typeof this.getLogs>[0] = {}
  ): string {
    const logs = this.getLogs(filters);

    switch (format) {
      case 'json':
        return JSON.stringify(logs, null, 2);
      
      case 'csv':
        return this.convertToCSV(logs);
      
      case 'xml':
        return this.convertToXML(logs);
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Get audit statistics
   */
  getStatistics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): {
    totalEvents: number;
    eventsByCategory: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    topUsers: Array<{ userId: string; count: number }>;
    topResources: Array<{ resource: string; count: number }>;
    errorRate: number;
    timeline: Array<{ timestamp: string; count: number }>;
  } {
    const now = new Date();
    const timeRangeMs = {
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
    }[timeRange];

    const cutoffTime = new Date(now.getTime() - timeRangeMs).toISOString();
    const recentLogs = this.logs.filter(log => log.timestamp >= cutoffTime);

    // Calculate statistics
    const totalEvents = recentLogs.length;
    const eventsByCategory: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};
    const userCounts: Record<string, number> = {};
    const resourceCounts: Record<string, number> = {};
    let errorCount = 0;

    recentLogs.forEach(log => {
      // Category counts
      eventsByCategory[log.metadata.category] = (eventsByCategory[log.metadata.category] || 0) + 1;
      
      // Severity counts
      eventsBySeverity[log.metadata.severity] = (eventsBySeverity[log.metadata.severity] || 0) + 1;
      
      // User counts
      userCounts[log.userId] = (userCounts[log.userId] || 0) + 1;
      
      // Resource counts
      resourceCounts[log.resource] = (resourceCounts[log.resource] || 0) + 1;
      
      // Error count
      if (!log.success) {
        errorCount++;
      }
    });

    // Top users and resources
    const topUsers = Object.entries(userCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([userId, count]) => ({ userId, count }));

    const topResources = Object.entries(resourceCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([resource, count]) => ({ resource, count }));

    // Timeline (hourly buckets)
    const timeline = this.generateTimeline(recentLogs, timeRange);

    return {
      totalEvents,
      eventsByCategory,
      eventsBySeverity,
      topUsers,
      topResources,
      errorRate: totalEvents > 0 ? (errorCount / totalEvents) * 100 : 0,
      timeline,
    };
  }

  /**
   * Clear old logs
   */
  clearOldLogs(olderThanDays: number = 90): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const cutoffTime = cutoffDate.toISOString();

    const initialCount = this.logs.length;
    this.logs = this.logs.filter(log => log.timestamp >= cutoffTime);
    const removedCount = initialCount - this.logs.length;

    loggerHelpers.info('Audit logs cleaned up', {
      removedCount,
      remainingCount: this.logs.length,
      cutoffDate: cutoffTime,
    });

    return removedCount;
  }

  // Private methods

  private generateAuditId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private buildContext(partialContext: Partial<AuditContext> = {}): AuditContext {
    const currentUser = this.getCurrentUser();
    
    return {
      userId: partialContext.userId || currentUser?.id,
      sessionId: partialContext.sessionId || this.getSessionId(),
      ip: partialContext.ip || 'client-side',
      userAgent: partialContext.userAgent || navigator.userAgent,
      location: partialContext.location,
      device: partialContext.device || this.getDeviceInfo(),
    };
  }

  private buildMetadata(partialMetadata: Partial<AuditMetadata> = {}): AuditMetadata {
    return {
      severity: partialMetadata.severity || 'low',
      category: partialMetadata.category || 'user',
      duration: partialMetadata.duration,
      requestId: partialMetadata.requestId,
      correlationId: partialMetadata.correlationId,
      tags: partialMetadata.tags || [],
    };
  }

  private sanitizeValues(values: Record<string, any> | undefined): Record<string, any> | undefined {
    if (!values) return undefined;

    const sanitized: Record<string, any> = {};
    
    Object.entries(values).forEach(([key, value]) => {
      if (SECURITY_CONFIG.AUDIT.sensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }

  private addLog(log: DetailedAuditLog): void {
    this.logs.unshift(log);
    
    // Maintain max logs limit
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }
  }

  private logToConsole(log: DetailedAuditLog): void {
    if (import.meta.env.NODE_ENV === 'development') {
      const color = log.metadata.severity === 'critical' ? 'red' : 
                   log.metadata.severity === 'high' ? 'orange' : 
                   log.metadata.severity === 'medium' ? 'yellow' : 'blue';
      
      console.log(
        `%c[AUDIT] ${log.action} on ${log.resource}`,
        `color: ${color}`,
        log
      );
    }
  }

  private getCurrentUser(): User | null {
    // This would typically come from Redux store
    try {
      const authData = secureStorage.getItem('auth_user', true);
      return authData;
    } catch {
      return null;
    }
  }

  private getSessionId(): string {
    return secureStorage.getItem('session_id', false) || 'unknown';
  }

  private getDeviceInfo(): AuditContext['device'] {
    const userAgent = navigator.userAgent;
    
    let type: 'desktop' | 'mobile' | 'tablet' = 'desktop';
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      type = /iPad/.test(userAgent) ? 'tablet' : 'mobile';
    }

    return {
      type,
      os: this.getOS(userAgent),
      browser: this.getBrowser(userAgent),
    };
  }

  private getOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private getBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private convertToCSV(logs: DetailedAuditLog[]): string {
    const headers = [
      'ID', 'Timestamp', 'User ID', 'Action', 'Resource', 'Resource ID',
      'Success', 'Error', 'IP', 'User Agent', 'Severity', 'Category'
    ];

    const rows = logs.map(log => [
      log.id,
      log.timestamp,
      log.userId,
      log.action,
      log.resource,
      log.resourceId || '',
      log.success,
      log.error || '',
      log.ip,
      log.userAgent,
      log.metadata.severity,
      log.metadata.category,
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');
  }

  private convertToXML(logs: DetailedAuditLog[]): string {
    const xmlLogs = logs.map(log => `
    <log>
      <id>${log.id}</id>
      <timestamp>${log.timestamp}</timestamp>
      <userId>${log.userId}</userId>
      <action>${log.action}</action>
      <resource>${log.resource}</resource>
      <resourceId>${log.resourceId || ''}</resourceId>
      <success>${log.success}</success>
      <error>${log.error || ''}</error>
      <ip>${log.ip}</ip>
      <userAgent><![CDATA[${log.userAgent}]]></userAgent>
      <severity>${log.metadata.severity}</severity>
      <category>${log.metadata.category}</category>
    </log>`).join('');

    return `<?xml version="1.0" encoding="UTF-8"?>
<auditLogs>
  ${xmlLogs}
</auditLogs>`;
  }

  private generateTimeline(logs: DetailedAuditLog[], timeRange: string): Array<{ timestamp: string; count: number }> {
    const buckets: Record<string, number> = {};
    const bucketSize = timeRange === 'hour' ? 5 * 60 * 1000 : // 5 minutes
                      timeRange === 'day' ? 60 * 60 * 1000 : // 1 hour
                      timeRange === 'week' ? 24 * 60 * 60 * 1000 : // 1 day
                      7 * 24 * 60 * 60 * 1000; // 1 week

    logs.forEach(log => {
      const timestamp = new Date(log.timestamp);
      const bucketTime = new Date(Math.floor(timestamp.getTime() / bucketSize) * bucketSize);
      const bucketKey = bucketTime.toISOString();
      
      buckets[bucketKey] = (buckets[bucketKey] || 0) + 1;
    });

    return Object.entries(buckets)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([timestamp, count]) => ({ timestamp, count }));
  }

  private startPeriodicFlush(): void {
    this.flushTimer = setInterval(() => {
      this.flushToServer();
    }, this.flushInterval);
  }

  private setupUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      this.flushToServer();
    });
  }

  private async flushToServer(): Promise<void> {
    if (!SECURITY_CONFIG.AUDIT.enabled || this.logs.length === 0) {
      return;
    }

    try {
      // In a real implementation, this would send logs to the server
      const logsToFlush = this.logs.slice(0, this.batchSize);
      
      // Mock API call
      console.log('Flushing audit logs to server:', logsToFlush.length);
      
      // Remove flushed logs
      this.logs = this.logs.slice(this.batchSize);
    } catch (error) {
      loggerHelpers.error('Failed to flush audit logs', error);
    }
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();
export default auditLogger;
