/**
 * Authentication and Authorization Middleware
 * Provides middleware functions for API route protection
 */

import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import type {
  UserContext,
  PermissionAction,
  RoutePermission,
  RBACError,
} from '../types';
import { rbacService } from '../services/rbac.service';
import { RBAC_ERROR_MESSAGES, HTTP_STATUS } from '../constants';
import { createUnauthorizedResponse, createForbiddenResponse } from '../guards/route-guard';

/**
 * Authentication middleware - validates token and provides user context
 */
export function withAuth<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  handler: (args: T & { userContext: UserContext }) => Promise<Response>
) {
  return async (args: T): Promise<Response> => {
    try {
      const token = getTokenFromRequest(args.request);
      
      if (!token) {
        return createUnauthorizedResponse('Authentication token required');
      }

      const userContext = await rbacService.validateTokenAndBuildContext(token);
      
      if (!userContext) {
        return createUnauthorizedResponse('Invalid or expired token');
      }

      // Add user context to args and call handler
      return handler({ ...args, userContext });
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return createUnauthorizedResponse('Authentication failed');
    }
  };
}

/**
 * Permission middleware - checks specific permissions
 */
export function withPermissions<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  permissions: RoutePermission[],
  requireAll: boolean = true
) {
  return function(
    handler: (args: T & { userContext: UserContext }) => Promise<Response>
  ) {
    return withAuth<T>(async (args) => {
      try {
        // Check permissions
        for (const permission of permissions) {
          const hasPermission = await checkPermissionMiddleware(
            args.userContext,
            permission
          );
          
          if (requireAll && !hasPermission) {
            return createForbiddenResponse(
              `Missing required permission: ${permission.resource}:${permission.actions.join(',')}`
            );
          } else if (!requireAll && hasPermission) {
            // At least one permission matches, proceed
            return handler(args);
          }
        }

        if (!requireAll) {
          // No permissions matched
          return createForbiddenResponse('Insufficient permissions');
        }

        // All permissions matched (requireAll = true)
        return handler(args);
      } catch (error) {
        console.error('Permission middleware error:', error);
        return createForbiddenResponse('Permission check failed');
      }
    });
  };
}

/**
 * Role middleware - checks specific roles
 */
export function withRoles<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  roles: string[],
  requireAll: boolean = false
) {
  return function(
    handler: (args: T & { userContext: UserContext }) => Promise<Response>
  ) {
    return withAuth<T>(async (args) => {
      try {
        const hasRequiredRoles = requireAll
          ? await rbacService.checkAllRoles(args.userContext, roles)
          : await rbacService.checkAnyRole(args.userContext, roles);

        if (!hasRequiredRoles) {
          return createForbiddenResponse(
            `Missing required role(s): ${roles.join(', ')}`
          );
        }

        return handler(args);
      } catch (error) {
        console.error('Role middleware error:', error);
        return createForbiddenResponse('Role check failed');
      }
    });
  };
}

/**
 * Admin middleware - requires admin or super admin role
 */
export function withAdmin<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  handler: (args: T & { userContext: UserContext }) => Promise<Response>
) {
  return withRoles<T>(['admin', 'super_admin'], false)(handler);
}

/**
 * Super admin middleware - requires super admin role
 */
export function withSuperAdmin<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  handler: (args: T & { userContext: UserContext }) => Promise<Response>
) {
  return withRoles<T>(['super_admin'], true)(handler);
}

/**
 * Resource owner middleware - checks resource ownership or admin privileges
 */
export function withResourceOwner<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  resourceType: string,
  getResourceId: (args: T) => string | undefined,
  ownerField: string = 'userId'
) {
  return function(
    handler: (args: T & { userContext: UserContext }) => Promise<Response>
  ) {
    return withAuth<T>(async (args) => {
      try {
        // Check if user is admin (can access any resource)
        const isAdmin = await rbacService.checkAnyRole(
          args.userContext,
          ['admin', 'super_admin']
        );
        
        if (isAdmin) {
          return handler(args);
        }

        // Check resource ownership
        const resourceId = getResourceId(args);
        if (!resourceId) {
          return createForbiddenResponse('Resource ID required');
        }

        const hasPermission = await rbacService.checkPermission(
          args.userContext,
          resourceType,
          'read',
          resourceId,
          { [ownerField]: args.userContext.user.id }
        );

        if (!hasPermission.allowed) {
          return createForbiddenResponse('Access denied to resource');
        }

        return handler(args);
      } catch (error) {
        console.error('Resource owner middleware error:', error);
        return createForbiddenResponse('Resource access check failed');
      }
    });
  };
}

/**
 * Tenant middleware - ensures user has access to specific tenant
 */
export function withTenant<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  getTenantId: (args: T) => string | undefined
) {
  return function(
    handler: (args: T & { userContext: UserContext }) => Promise<Response>
  ) {
    return withAuth<T>(async (args) => {
      try {
        const tenantId = getTenantId(args);
        
        if (tenantId && args.userContext.currentTenant.id !== tenantId) {
          return createForbiddenResponse('Access denied to tenant');
        }

        return handler(args);
      } catch (error) {
        console.error('Tenant middleware error:', error);
        return createForbiddenResponse('Tenant access check failed');
      }
    });
  };
}

/**
 * Optional auth middleware - provides user context if available
 */
export function withOptionalAuth<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  handler: (args: T & { userContext: UserContext | null }) => Promise<Response>
) {
  return async (args: T): Promise<Response> => {
    try {
      const token = getTokenFromRequest(args.request);
      let userContext: UserContext | null = null;
      
      if (token) {
        userContext = await rbacService.validateTokenAndBuildContext(token);
      }

      return handler({ ...args, userContext });
    } catch (error) {
      // If optional auth fails, continue with null context
      return handler({ ...args, userContext: null });
    }
  };
}

/**
 * Rate limiting middleware (basic implementation)
 */
export function withRateLimit<T extends LoaderFunctionArgs | ActionFunctionArgs>(
  maxRequests: number = 100,
  windowMs: number = 60000 // 1 minute
) {
  const requestCounts = new Map<string, { count: number; resetTime: number }>();

  return function(
    handler: (args: T) => Promise<Response>
  ) {
    return async (args: T): Promise<Response> => {
      const clientId = getClientId(args.request);
      const now = Date.now();
      
      const clientData = requestCounts.get(clientId);
      
      if (!clientData || now > clientData.resetTime) {
        // Reset or initialize counter
        requestCounts.set(clientId, {
          count: 1,
          resetTime: now + windowMs,
        });
      } else {
        // Increment counter
        clientData.count++;
        
        if (clientData.count > maxRequests) {
          return new Response(
            JSON.stringify({
              error: {
                code: 'RATE_LIMIT_EXCEEDED',
                message: 'Too many requests',
                statusCode: 429,
              },
            }),
            {
              status: 429,
              headers: {
                'Content-Type': 'application/json',
                'Retry-After': Math.ceil((clientData.resetTime - now) / 1000).toString(),
              },
            }
          );
        }
      }

      return handler(args);
    };
  };
}

/**
 * Helper function to check permission in middleware
 */
async function checkPermissionMiddleware(
  userContext: UserContext,
  permission: RoutePermission
): Promise<boolean> {
  const permissionChecks = permission.actions.map(action =>
    rbacService.checkPermission(userContext, permission.resource, action)
  );

  const results = await Promise.all(permissionChecks);
  
  return permission.requireAll
    ? results.every(result => result.allowed)
    : results.some(result => result.allowed);
}

/**
 * Extract authentication token from request
 */
function getTokenFromRequest(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try cookie
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = parseCookies(cookieHeader);
    return cookies.auth_token || null;
  }

  return null;
}

/**
 * Get client ID for rate limiting
 */
function getClientId(request: Request): string {
  // Try to get IP address from various headers
  const forwarded = request.headers.get('X-Forwarded-For');
  const realIp = request.headers.get('X-Real-IP');
  const remoteAddr = request.headers.get('Remote-Addr');
  
  return forwarded?.split(',')[0] || realIp || remoteAddr || 'unknown';
}

/**
 * Parse cookies from cookie header
 */
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}
