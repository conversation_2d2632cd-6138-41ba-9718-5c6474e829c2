/**
 * Route Guard System for React Router v7
 * Provides authentication and authorization guards for routes
 */

import { redirect } from 'react-router';
import type { LoaderFunctionArgs } from 'react-router';
import type {
  UserContext,
  RoutePermission,
  ProtectedRouteConfig,
  PermissionAction,
  RBACError,
} from '../types';
import { rbacService } from '../services/rbac.service';
import { REDIRECT_PATHS, RBAC_ERROR_MESSAGES } from '../constants';

/**
 * Authentication guard - ensures user is authenticated
 */
export async function requireAuth({ request }: LoaderFunctionArgs): Promise<UserContext> {
  const token = getTokenFromRequest(request);
  
  if (!token) {
    throw redirect(REDIRECT_PATHS.LOGIN);
  }

  const userContext = await rbacService.validateTokenAndBuildContext(token);
  
  if (!userContext) {
    throw redirect(REDIRECT_PATHS.LOGIN);
  }

  return userContext;
}

/**
 * Permission guard - ensures user has required permissions
 */
export async function requirePermissions(
  permissions: RoutePermission[],
  requireAll: boolean = true
) {
  return async ({ request }: LoaderFunctionArgs): Promise<UserContext> => {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);

    for (const permission of permissions) {
      const hasPermission = await checkRoutePermission(userContext, permission);
      
      if (requireAll && !hasPermission) {
        throw redirect(REDIRECT_PATHS.FORBIDDEN);
      } else if (!requireAll && hasPermission) {
        return userContext; // At least one permission matches
      }
    }

    if (!requireAll) {
      // No permissions matched
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Role guard - ensures user has required roles
 */
export async function requireRoles(
  roles: string[],
  requireAll: boolean = false
) {
  return async ({ request }: LoaderFunctionArgs): Promise<UserContext> => {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);

    const hasRequiredRoles = requireAll
      ? await rbacService.checkAllRoles(userContext, roles)
      : await rbacService.checkAnyRole(userContext, roles);

    if (!hasRequiredRoles) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Tenant guard - ensures user has access to specific tenant
 */
export async function requireTenant(tenantSlug?: string) {
  return async ({ request, params }: LoaderFunctionArgs): Promise<UserContext> => {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);
    
    // Get tenant slug from params or parameter
    const targetTenantSlug = tenantSlug || params.tenantSlug;
    
    if (targetTenantSlug && userContext.currentTenant.slug !== targetTenantSlug) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Admin guard - ensures user has admin role
 */
export async function requireAdmin({ request }: LoaderFunctionArgs): Promise<UserContext> {
  return requireRoles(['admin', 'super_admin'], false)({ request } as LoaderFunctionArgs);
}

/**
 * Super admin guard - ensures user has super admin role
 */
export async function requireSuperAdmin({ request }: LoaderFunctionArgs): Promise<UserContext> {
  return requireRoles(['super_admin'], true)({ request } as LoaderFunctionArgs);
}

/**
 * Resource owner guard - ensures user owns the resource or has admin privileges
 */
export async function requireResourceOwner(
  resourceType: string,
  ownerField: string = 'userId'
) {
  return async ({ request, params }: LoaderFunctionArgs): Promise<UserContext> => {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);

    // Check if user is admin (can access any resource)
    const isAdmin = await rbacService.checkAnyRole(userContext, ['admin', 'super_admin']);
    if (isAdmin) {
      return userContext;
    }

    // Check resource ownership
    const resourceId = params.id;
    if (!resourceId) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    // In a real implementation, you would fetch the resource and check ownership
    // For now, we'll assume the resource exists and check basic permission
    const hasPermission = await rbacService.checkPermission(
      userContext,
      resourceType,
      'read',
      resourceId,
      { [ownerField]: userContext.user.id }
    );

    if (!hasPermission.allowed) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Combined guard - allows multiple guard conditions
 */
export function combineGuards(...guards: Array<(args: LoaderFunctionArgs) => Promise<UserContext>>) {
  return async (args: LoaderFunctionArgs): Promise<UserContext> => {
    let userContext: UserContext | null = null;

    for (const guard of guards) {
      try {
        userContext = await guard(args);
        break; // If any guard passes, we're good
      } catch (error) {
        // Continue to next guard
        continue;
      }
    }

    if (!userContext) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Optional auth guard - provides user context if available, but doesn't require it
 */
export async function optionalAuth({ request }: LoaderFunctionArgs): Promise<UserContext | null> {
  try {
    const token = getTokenFromRequest(request);
    
    if (!token) {
      return null;
    }

    return await rbacService.validateTokenAndBuildContext(token);
  } catch (error) {
    return null;
  }
}

/**
 * Create a protected route configuration
 */
export function createProtectedRoute(config: ProtectedRouteConfig) {
  return async (args: LoaderFunctionArgs): Promise<UserContext> => {
    const userContext = await requireAuth(args);

    // Check permissions if specified
    if (config.permissions && config.permissions.length > 0) {
      const permissionChecks = config.permissions.map(permission =>
        checkRoutePermission(userContext, permission)
      );

      const results = await Promise.all(permissionChecks);
      const hasRequiredPermissions = config.requireAll
        ? results.every(result => result)
        : results.some(result => result);

      if (!hasRequiredPermissions) {
        if (config.redirectOnUnauthorized) {
          throw redirect(config.redirectOnUnauthorized);
        }
        throw redirect(REDIRECT_PATHS.FORBIDDEN);
      }
    }

    // Check roles if specified
    if (config.roles && config.roles.length > 0) {
      const hasRequiredRoles = config.requireAll
        ? await rbacService.checkAllRoles(userContext, config.roles)
        : await rbacService.checkAnyRole(userContext, config.roles);

      if (!hasRequiredRoles) {
        if (config.redirectOnUnauthorized) {
          throw redirect(config.redirectOnUnauthorized);
        }
        throw redirect(REDIRECT_PATHS.FORBIDDEN);
      }
    }

    // Check tenant requirement
    if (config.tenantRequired && !userContext.currentTenant) {
      throw redirect(REDIRECT_PATHS.FORBIDDEN);
    }

    return userContext;
  };
}

/**
 * Helper function to check route permission
 */
async function checkRoutePermission(
  userContext: UserContext,
  permission: RoutePermission
): Promise<boolean> {
  const permissionChecks = permission.actions.map(action =>
    rbacService.checkPermission(userContext, permission.resource, action)
  );

  const results = await Promise.all(permissionChecks);
  
  return permission.requireAll
    ? results.every(result => result.allowed)
    : results.some(result => result.allowed);
}

/**
 * Extract authentication token from request
 */
function getTokenFromRequest(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try cookie (for browser requests)
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = parseCookies(cookieHeader);
    return cookies.auth_token || null;
  }

  return null;
}

/**
 * Parse cookies from cookie header
 */
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}

/**
 * Create error response for unauthorized access
 */
export function createUnauthorizedResponse(
  message: string = RBAC_ERROR_MESSAGES.UNAUTHORIZED,
  statusCode: number = 401
): Response {
  return new Response(
    JSON.stringify({
      error: {
        code: 'UNAUTHORIZED',
        message,
        statusCode,
      },
    }),
    {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}

/**
 * Create error response for forbidden access
 */
export function createForbiddenResponse(
  message: string = RBAC_ERROR_MESSAGES.FORBIDDEN,
  statusCode: number = 403
): Response {
  return new Response(
    JSON.stringify({
      error: {
        code: 'FORBIDDEN',
        message,
        statusCode,
      },
    }),
    {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}
