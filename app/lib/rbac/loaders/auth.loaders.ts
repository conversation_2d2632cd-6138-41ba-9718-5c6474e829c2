/**
 * Authentication Loaders for React Router v7
 * Handles authentication-related data loading and actions
 */

import { redirect, json } from 'react-router';
import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import type {
  UserContext,
  AuthenticationRequest,
  AuthenticationResponse,
  RBACError,
} from '../types';
import { rbacService } from '../services/rbac.service';
import { REDIRECT_PATHS } from '../constants';
import { requireAuth, optionalAuth } from '../guards/route-guard';

/**
 * Authentication loader - provides user context to routes
 */
export async function authLoader({ request }: LoaderFunctionArgs) {
  try {
    const userContext = await optionalAuth({ request } as LoaderFunctionArgs);
    
    return json({
      user: userContext,
      isAuthenticated: !!userContext,
    });
  } catch (error) {
    return json({
      user: null,
      isAuthenticated: false,
      error: error instanceof Error ? error.message : 'Authentication failed',
    });
  }
}

/**
 * Protected loader - requires authentication
 */
export async function protectedLoader({ request }: LoaderFunctionArgs) {
  try {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);
    
    return json({
      user: userContext,
      isAuthenticated: true,
    });
  } catch (error) {
    // This will be a redirect, so we re-throw it
    throw error;
  }
}

/**
 * Login action - handles user authentication
 */
export async function loginAction({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;
    const tenantSlug = formData.get('tenantSlug') as string | undefined;

    if (!email || !password) {
      return json(
        { 
          error: 'Email and password are required',
          success: false,
        },
        { status: 400 }
      );
    }

    const authRequest: AuthenticationRequest = {
      email,
      password,
      tenantSlug,
    };

    const authResponse = await rbacService.authenticate(authRequest);

    // Set authentication cookie
    const headers = new Headers();
    headers.append(
      'Set-Cookie',
      `auth_token=${authResponse.token}; HttpOnly; Secure; SameSite=Strict; Max-Age=3600; Path=/`
    );
    headers.append(
      'Set-Cookie',
      `refresh_token=${authResponse.refreshToken}; HttpOnly; Secure; SameSite=Strict; Max-Age=604800; Path=/`
    );

    return json(
      {
        success: true,
        user: authResponse.user,
        tenants: authResponse.tenants,
        defaultTenant: authResponse.defaultTenant,
      },
      { headers }
    );
  } catch (error) {
    const rbacError = error as RBACError;
    return json(
      {
        error: rbacError.message || 'Authentication failed',
        success: false,
      },
      { status: rbacError.statusCode || 401 }
    );
  }
}

/**
 * Logout action - handles user logout
 */
export async function logoutAction({ request }: ActionFunctionArgs) {
  try {
    const token = getTokenFromRequest(request);
    
    if (token) {
      await rbacService.logout(token);
    }

    // Clear authentication cookies
    const headers = new Headers();
    headers.append(
      'Set-Cookie',
      'auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/'
    );
    headers.append(
      'Set-Cookie',
      'refresh_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/'
    );

    return redirect(REDIRECT_PATHS.DEFAULT_AFTER_LOGOUT, { headers });
  } catch (error) {
    // Even if logout fails, redirect to login
    return redirect(REDIRECT_PATHS.DEFAULT_AFTER_LOGOUT);
  }
}

/**
 * Refresh token action - handles token refresh
 */
export async function refreshTokenAction({ request }: ActionFunctionArgs) {
  try {
    const refreshToken = getRefreshTokenFromRequest(request);
    
    if (!refreshToken) {
      return json(
        { error: 'Refresh token not found', success: false },
        { status: 401 }
      );
    }

    const result = await rbacService.refreshToken(refreshToken);

    // Set new authentication cookie
    const headers = new Headers();
    headers.append(
      'Set-Cookie',
      `auth_token=${result.token}; HttpOnly; Secure; SameSite=Strict; Max-Age=3600; Path=/`
    );

    return json(
      {
        success: true,
        token: result.token,
        expiresAt: result.expiresAt,
      },
      { headers }
    );
  } catch (error) {
    const rbacError = error as RBACError;
    return json(
      {
        error: rbacError.message || 'Token refresh failed',
        success: false,
      },
      { status: rbacError.statusCode || 401 }
    );
  }
}

/**
 * User profile loader - loads current user profile
 */
export async function userProfileLoader({ request }: LoaderFunctionArgs) {
  try {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);
    
    const permissionSummary = rbacService.getPermissionSummary(userContext);
    
    return json({
      user: userContext.user,
      currentTenant: userContext.currentTenant,
      effectiveRole: userContext.effectiveRole,
      permissions: permissionSummary,
      tenantRoles: userContext.tenantRoles,
    });
  } catch (error) {
    throw error; // Re-throw redirect
  }
}

/**
 * Tenant switch action - switches user's current tenant
 */
export async function switchTenantAction({ request }: ActionFunctionArgs) {
  try {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);
    const formData = await request.formData();
    const tenantId = formData.get('tenantId') as string;

    if (!tenantId) {
      return json(
        { error: 'Tenant ID is required', success: false },
        { status: 400 }
      );
    }

    // Check if user has access to the tenant
    const userTenantRoles = userContext.tenantRoles.filter(
      utr => utr.tenantId === tenantId && utr.isActive
    );

    if (userTenantRoles.length === 0) {
      return json(
        { error: 'Access denied to tenant', success: false },
        { status: 403 }
      );
    }

    // In a real implementation, you would create a new token with the new tenant
    // For now, we'll just return success
    return json({
      success: true,
      message: 'Tenant switched successfully',
    });
  } catch (error) {
    const rbacError = error as RBACError;
    return json(
      {
        error: rbacError.message || 'Tenant switch failed',
        success: false,
      },
      { status: rbacError.statusCode || 500 }
    );
  }
}

/**
 * Role assignment action - assigns role to user (admin only)
 */
export async function assignRoleAction({ request }: ActionFunctionArgs) {
  try {
    const userContext = await requireAuth({ request } as LoaderFunctionArgs);
    
    // Check if user has admin privileges
    const isAdmin = await rbacService.checkAnyRole(userContext, ['admin', 'super_admin']);
    if (!isAdmin) {
      return json(
        { error: 'Insufficient privileges', success: false },
        { status: 403 }
      );
    }

    const formData = await request.formData();
    const userId = formData.get('userId') as string;
    const roleId = formData.get('roleId') as string;
    const tenantId = formData.get('tenantId') as string;

    if (!userId || !roleId || !tenantId) {
      return json(
        { error: 'User ID, Role ID, and Tenant ID are required', success: false },
        { status: 400 }
      );
    }

    const assignment = await rbacService.assignRoleToUser(
      userId,
      tenantId,
      roleId,
      userContext.user.id
    );

    return json({
      success: true,
      assignment,
      message: 'Role assigned successfully',
    });
  } catch (error) {
    const rbacError = error as RBACError;
    return json(
      {
        error: rbacError.message || 'Role assignment failed',
        success: false,
      },
      { status: rbacError.statusCode || 500 }
    );
  }
}

/**
 * Helper function to extract token from request
 */
function getTokenFromRequest(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try cookie
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = parseCookies(cookieHeader);
    return cookies.auth_token || null;
  }

  return null;
}

/**
 * Helper function to extract refresh token from request
 */
function getRefreshTokenFromRequest(request: Request): string | null {
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = parseCookies(cookieHeader);
    return cookies.refresh_token || null;
  }

  return null;
}

/**
 * Parse cookies from cookie header
 */
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}
