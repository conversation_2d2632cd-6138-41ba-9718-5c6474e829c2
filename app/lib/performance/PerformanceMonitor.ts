/**
 * Performance Monitoring System
 * Comprehensive performance tracking and optimization
 */

import { loggerHelpers } from '../../store/middleware/loggingMiddleware';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

interface PerformanceReport {
  metrics: PerformanceMetric[];
  vitals: {
    fcp: number | null; // First Contentful Paint
    lcp: number | null; // Largest Contentful Paint
    fid: number | null; // First Input Delay
    cls: number | null; // Cumulative Layout Shift
    ttfb: number | null; // Time to First Byte
  };
  resources: {
    totalSize: number;
    totalRequests: number;
    slowestResource: string | null;
    largestResource: string | null;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  } | null;
  timestamp: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private timers: Map<string, number> = new Map();
  private counters: Map<string, number> = new Map();
  private isMonitoring = false;

  constructor() {
    this.setupPerformanceObservers();
    this.startMonitoring();
  }

  /**
   * Setup performance observers
   */
  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) {
      loggerHelpers.warn('PerformanceObserver not supported');
      return;
    }

    // Navigation timing
    try {
      const navObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric('navigation.domContentLoaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart, 'timing');
            this.recordMetric('navigation.loadComplete', navEntry.loadEventEnd - navEntry.loadEventStart, 'timing');
            this.recordMetric('navigation.ttfb', navEntry.responseStart - navEntry.requestStart, 'timing');
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup navigation observer', error);
    }

    // Resource timing
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.recordMetric(`resource.${this.getResourceType(resourceEntry.name)}`, resourceEntry.duration, 'timing', {
              url: resourceEntry.name,
              size: resourceEntry.transferSize?.toString() || '0',
            });
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup resource observer', error);
    }

    // Paint timing
    try {
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'paint') {
            this.recordMetric(`paint.${entry.name}`, entry.startTime, 'timing');
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup paint observer', error);
    }

    // Layout shift
    try {
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        });
        if (clsValue > 0) {
          this.recordMetric('vitals.cls', clsValue, 'gauge');
        }
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup layout shift observer', error);
    }

    // Largest Contentful Paint
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          this.recordMetric('vitals.lcp', lastEntry.startTime, 'timing');
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup LCP observer', error);
    }

    // First Input Delay
    try {
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'first-input') {
            const fidValue = (entry as any).processingStart - entry.startTime;
            this.recordMetric('vitals.fid', fidValue, 'timing');
          }
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup FID observer', error);
    }
  }

  /**
   * Start monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Monitor memory usage
    this.startMemoryMonitoring();

    // Monitor frame rate
    this.startFrameRateMonitoring();

    // Monitor long tasks
    this.startLongTaskMonitoring();

    loggerHelpers.info('Performance monitoring started');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    loggerHelpers.info('Performance monitoring stopped');
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, type: PerformanceMetric['type'], tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags,
    };

    this.metrics.push(metric);

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log significant metrics
    if (this.isSignificantMetric(name, value)) {
      loggerHelpers.info('Performance metric recorded', metric);
    }
  }

  /**
   * Start a timer
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * End a timer and record the duration
   */
  endTimer(name: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      loggerHelpers.warn('Timer not found', { name });
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);
    this.recordMetric(`timer.${name}`, duration, 'timing', tags);

    return duration;
  }

  /**
   * Increment a counter
   */
  incrementCounter(name: string, value = 1, tags?: Record<string, string>): void {
    const currentValue = this.counters.get(name) || 0;
    const newValue = currentValue + value;
    this.counters.set(name, newValue);
    this.recordMetric(`counter.${name}`, newValue, 'counter', tags);
  }

  /**
   * Set a gauge value
   */
  setGauge(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric(`gauge.${name}`, value, 'gauge', tags);
  }

  /**
   * Get performance report
   */
  getPerformanceReport(): PerformanceReport {
    const vitals = this.getWebVitals();
    const resources = this.getResourceMetrics();
    const memory = this.getMemoryInfo();

    return {
      metrics: [...this.metrics],
      vitals,
      resources,
      memory,
      timestamp: Date.now(),
    };
  }

  /**
   * Get Web Vitals
   */
  private getWebVitals() {
    const getLatestMetric = (name: string) => {
      const metrics = this.metrics.filter(m => m.name === name);
      return metrics.length > 0 ? metrics[metrics.length - 1].value : null;
    };

    return {
      fcp: getLatestMetric('paint.first-contentful-paint'),
      lcp: getLatestMetric('vitals.lcp'),
      fid: getLatestMetric('vitals.fid'),
      cls: getLatestMetric('vitals.cls'),
      ttfb: getLatestMetric('navigation.ttfb'),
    };
  }

  /**
   * Get resource metrics
   */
  private getResourceMetrics() {
    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    let totalSize = 0;
    let slowestResource = '';
    let slowestDuration = 0;
    let largestResource = '';
    let largestSize = 0;

    resourceEntries.forEach(entry => {
      const size = entry.transferSize || 0;
      totalSize += size;

      if (entry.duration > slowestDuration) {
        slowestDuration = entry.duration;
        slowestResource = entry.name;
      }

      if (size > largestSize) {
        largestSize = size;
        largestResource = entry.name;
      }
    });

    return {
      totalSize,
      totalRequests: resourceEntries.length,
      slowestResource: slowestResource || null,
      largestResource: largestResource || null,
    };
  }

  /**
   * Get memory information
   */
  private getMemoryInfo() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100),
      };
    }
    return null;
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    if (!('memory' in performance)) return;

    const monitorMemory = () => {
      const memory = (performance as any).memory;
      const usagePercentage = (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100;
      
      this.setGauge('memory.usage', usagePercentage);
      this.setGauge('memory.used', memory.usedJSHeapSize);
      this.setGauge('memory.total', memory.totalJSHeapSize);

      if (this.isMonitoring) {
        setTimeout(monitorMemory, 5000); // Check every 5 seconds
      }
    };

    monitorMemory();
  }

  /**
   * Start frame rate monitoring
   */
  private startFrameRateMonitoring(): void {
    let lastTime = performance.now();
    let frameCount = 0;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) { // Every second
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.setGauge('performance.fps', fps);
        
        frameCount = 0;
        lastTime = currentTime;
      }

      if (this.isMonitoring) {
        requestAnimationFrame(measureFPS);
      }
    };

    requestAnimationFrame(measureFPS);
  }

  /**
   * Start long task monitoring
   */
  private startLongTaskMonitoring(): void {
    if (!('PerformanceObserver' in window)) return;

    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'longtask') {
            this.recordMetric('performance.longTask', entry.duration, 'timing', {
              name: entry.name,
            });
            
            if (entry.duration > 100) { // Tasks longer than 100ms
              loggerHelpers.warn('Long task detected', {
                duration: entry.duration,
                name: entry.name,
              });
            }
          }
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    } catch (error) {
      loggerHelpers.error('Failed to setup long task observer', error);
    }
  }

  /**
   * Get resource type from URL
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    if (url.includes('/api/')) return 'api';
    return 'other';
  }

  /**
   * Check if metric is significant
   */
  private isSignificantMetric(name: string, value: number): boolean {
    const significantThresholds: Record<string, number> = {
      'paint.first-contentful-paint': 2000,
      'vitals.lcp': 2500,
      'vitals.fid': 100,
      'vitals.cls': 0.1,
      'navigation.ttfb': 600,
      'memory.usage': 80,
      'performance.longTask': 100,
    };

    const threshold = significantThresholds[name];
    return threshold !== undefined && value > threshold;
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    const report = this.getPerformanceReport();
    return JSON.stringify(report, null, 2);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.counters.clear();
    this.timers.clear();
    loggerHelpers.info('Performance metrics cleared');
  }

  /**
   * Get metrics by type
   */
  getMetricsByType(type: PerformanceMetric['type']): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  /**
   * Get metrics by name pattern
   */
  getMetricsByPattern(pattern: RegExp): PerformanceMetric[] {
    return this.metrics.filter(metric => pattern.test(metric.name));
  }

  /**
   * Get average value for a metric
   */
  getAverageMetric(name: string): number {
    const metrics = this.metrics.filter(m => m.name === name);
    if (metrics.length === 0) return 0;
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Auto-start monitoring in production
if (import.meta.env.NODE_ENV === 'production') {
  performanceMonitor.startMonitoring();
}

export default performanceMonitor;
