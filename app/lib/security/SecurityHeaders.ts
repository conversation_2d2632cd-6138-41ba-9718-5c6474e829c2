/**
 * Security Headers Implementation
 * Comprehensive security headers and CSP configuration
 */

import { SECURITY_CONFIG } from '../../config/security';

interface SecurityHeadersConfig {
  contentSecurityPolicy?: boolean;
  strictTransportSecurity?: boolean;
  xFrameOptions?: boolean;
  xContentTypeOptions?: boolean;
  xXSSProtection?: boolean;
  referrerPolicy?: boolean;
  permissionsPolicy?: boolean;
  crossOriginEmbedderPolicy?: boolean;
  crossOriginOpenerPolicy?: boolean;
  crossOriginResourcePolicy?: boolean;
}

class SecurityHeaders {
  private config: SecurityHeadersConfig;

  constructor(config: SecurityHeadersConfig = {}) {
    this.config = {
      contentSecurityPolicy: true,
      strictTransportSecurity: true,
      xFrameOptions: true,
      xContentTypeOptions: true,
      xXSSProtection: true,
      referrerPolicy: true,
      permissionsPolicy: true,
      crossOriginEmbedderPolicy: false,
      crossOriginOpenerPolicy: false,
      crossOriginResourcePolicy: false,
      ...config,
    };
  }

  /**
   * Generate Content Security Policy header
   */
  generateCSP(): string {
    const directives = SECURITY_CONFIG.CSP.directives;
    
    const cspParts: string[] = [];
    
    Object.entries(directives).forEach(([directive, sources]) => {
      const directiveName = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
      const sourceList = Array.isArray(sources) ? sources.join(' ') : sources;
      cspParts.push(`${directiveName} ${sourceList}`);
    });

    return cspParts.join('; ');
  }

  /**
   * Generate all security headers
   */
  generateHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    // Content Security Policy
    if (this.config.contentSecurityPolicy) {
      headers['Content-Security-Policy'] = this.generateCSP();
    }

    // Strict Transport Security
    if (this.config.strictTransportSecurity) {
      headers['Strict-Transport-Security'] = SECURITY_CONFIG.HEADERS['Strict-Transport-Security'];
    }

    // X-Frame-Options
    if (this.config.xFrameOptions) {
      headers['X-Frame-Options'] = SECURITY_CONFIG.HEADERS['X-Frame-Options'];
    }

    // X-Content-Type-Options
    if (this.config.xContentTypeOptions) {
      headers['X-Content-Type-Options'] = SECURITY_CONFIG.HEADERS['X-Content-Type-Options'];
    }

    // X-XSS-Protection
    if (this.config.xXSSProtection) {
      headers['X-XSS-Protection'] = SECURITY_CONFIG.HEADERS['X-XSS-Protection'];
    }

    // Referrer Policy
    if (this.config.referrerPolicy) {
      headers['Referrer-Policy'] = SECURITY_CONFIG.HEADERS['Referrer-Policy'];
    }

    // Permissions Policy
    if (this.config.permissionsPolicy) {
      headers['Permissions-Policy'] = SECURITY_CONFIG.HEADERS['Permissions-Policy'];
    }

    // Cross-Origin Embedder Policy
    if (this.config.crossOriginEmbedderPolicy) {
      headers['Cross-Origin-Embedder-Policy'] = 'require-corp';
    }

    // Cross-Origin Opener Policy
    if (this.config.crossOriginOpenerPolicy) {
      headers['Cross-Origin-Opener-Policy'] = 'same-origin';
    }

    // Cross-Origin Resource Policy
    if (this.config.crossOriginResourcePolicy) {
      headers['Cross-Origin-Resource-Policy'] = 'same-origin';
    }

    return headers;
  }

  /**
   * Apply headers to document (client-side)
   */
  applyToDocument(): void {
    const headers = this.generateHeaders();
    
    // Apply CSP via meta tag if not already present
    if (headers['Content-Security-Policy'] && !document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = headers['Content-Security-Policy'];
      document.head.appendChild(meta);
    }

    // Log applied headers in development
    if (import.meta.env.NODE_ENV === 'development') {
      console.log('Security headers applied:', headers);
    }
  }

  /**
   * Validate current page against CSP
   */
  validateCSP(): {
    valid: boolean;
    violations: string[];
  } {
    const violations: string[] = [];

    // Check for inline scripts
    const inlineScripts = document.querySelectorAll('script:not([src])');
    if (inlineScripts.length > 0 && !SECURITY_CONFIG.CSP.directives.scriptSrc.includes("'unsafe-inline'")) {
      violations.push(`Found ${inlineScripts.length} inline script(s)`);
    }

    // Check for inline styles
    const inlineStyles = document.querySelectorAll('style, [style]');
    if (inlineStyles.length > 0 && !SECURITY_CONFIG.CSP.directives.styleSrc.includes("'unsafe-inline'")) {
      violations.push(`Found ${inlineStyles.length} inline style(s)`);
    }

    // Check for eval usage (this is a basic check)
    try {
      const originalEval = window.eval;
      let evalUsed = false;
      
      window.eval = function(...args) {
        evalUsed = true;
        return originalEval.apply(this, args);
      };
      
      // Restore original eval after a short delay
      setTimeout(() => {
        window.eval = originalEval;
        if (evalUsed && !SECURITY_CONFIG.CSP.directives.scriptSrc.includes("'unsafe-eval'")) {
          violations.push('eval() usage detected');
        }
      }, 100);
    } catch (error) {
      // Ignore errors in eval detection
    }

    return {
      valid: violations.length === 0,
      violations,
    };
  }

  /**
   * Monitor CSP violations
   */
  monitorCSPViolations(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      const violation = {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
        columnNumber: event.columnNumber,
        timestamp: new Date().toISOString(),
      };

      // Log violation
      console.warn('CSP Violation:', violation);

      // Report to server (in production)
      if (import.meta.env.NODE_ENV === 'production') {
        this.reportCSPViolation(violation);
      }
    });
  }

  /**
   * Report CSP violation to server
   */
  private async reportCSPViolation(violation: any): Promise<void> {
    try {
      await fetch('/api/security/csp-violation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(violation),
      });
    } catch (error) {
      console.error('Failed to report CSP violation:', error);
    }
  }

  /**
   * Check if running in secure context
   */
  isSecureContext(): boolean {
    return window.isSecureContext || location.protocol === 'https:';
  }

  /**
   * Validate security requirements
   */
  validateSecurityRequirements(): {
    valid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check HTTPS
    if (!this.isSecureContext() && import.meta.env.NODE_ENV === 'production') {
      issues.push('Not running in secure context (HTTPS required)');
    }

    // Check for mixed content
    if (location.protocol === 'https:') {
      const httpResources = Array.from(document.querySelectorAll('img, script, link, iframe'))
        .filter((element: any) => {
          const src = element.src || element.href;
          return src && src.startsWith('http:');
        });

      if (httpResources.length > 0) {
        issues.push(`Found ${httpResources.length} insecure resource(s) (mixed content)`);
      }
    }

    // Check for deprecated features
    if ('webkitStorageInfo' in window) {
      issues.push('Deprecated webkitStorageInfo API detected');
    }

    // Check for insecure storage usage
    try {
      if (localStorage.getItem('password') || sessionStorage.getItem('password')) {
        issues.push('Sensitive data found in browser storage');
      }
    } catch (error) {
      // Storage access might be blocked
    }

    return {
      valid: issues.length === 0,
      issues,
    };
  }

  /**
   * Generate security report
   */
  generateSecurityReport(): {
    headers: Record<string, string>;
    cspValidation: ReturnType<typeof this.validateCSP>;
    securityValidation: ReturnType<typeof this.validateSecurityRequirements>;
    secureContext: boolean;
    timestamp: string;
  } {
    return {
      headers: this.generateHeaders(),
      cspValidation: this.validateCSP(),
      securityValidation: this.validateSecurityRequirements(),
      secureContext: this.isSecureContext(),
      timestamp: new Date().toISOString(),
    };
  }
}

// Export singleton instance
export const securityHeaders = new SecurityHeaders();

// Auto-apply security headers on load
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    securityHeaders.applyToDocument();
    securityHeaders.monitorCSPViolations();
    
    // Validate security in development
    if (import.meta.env.NODE_ENV === 'development') {
      const report = securityHeaders.generateSecurityReport();
      console.log('Security Report:', report);
      
      if (!report.cspValidation.valid) {
        console.warn('CSP Violations:', report.cspValidation.violations);
      }
      
      if (!report.securityValidation.valid) {
        console.warn('Security Issues:', report.securityValidation.issues);
      }
    }
  });
}

export default securityHeaders;
