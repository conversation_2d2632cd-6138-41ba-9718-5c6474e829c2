/**
 * XSS Protection Utilities
 * Comprehensive XSS prevention and input sanitization
 */

import DOMPurify from 'dompurify';
import { SECURITY_CONFIG } from '../../config/security';

interface SanitizeOptions {
  allowedTags?: string[];
  allowedAttributes?: Record<string, string[]>;
  stripIgnoreTag?: boolean;
  stripIgnoreTagBody?: string[];
  forbiddenTags?: string[];
  forbiddenAttributes?: string[];
}

class XSSProtection {
  private purify: typeof DOMPurify;

  constructor() {
    this.purify = DOMPurify;
    this.configurePurify();
  }

  /**
   * Configure DOMPurify with security settings
   */
  private configurePurify(): void {
    // Add custom hooks
    this.purify.addHook('beforeSanitizeElements', (node) => {
      // Log potentially dangerous elements
      if (node.tagName && ['SCRIPT', 'IFRAME', 'OBJECT', 'EMBED'].includes(node.tagName)) {
        console.warn('XSS Protection: Removed dangerous element:', node.tagName);
      }
    });

    this.purify.addHook('afterSanitizeAttributes', (node) => {
      // Remove javascript: protocols
      if (node.hasAttribute('href') && node.getAttribute('href')?.startsWith('javascript:')) {
        node.removeAttribute('href');
        console.warn('XSS Protection: Removed javascript: protocol from href');
      }
      
      if (node.hasAttribute('src') && node.getAttribute('src')?.startsWith('javascript:')) {
        node.removeAttribute('src');
        console.warn('XSS Protection: Removed javascript: protocol from src');
      }
    });
  }

  /**
   * Sanitize HTML content
   */
  sanitizeHTML(
    dirty: string,
    options: SanitizeOptions = {}
  ): string {
    const config = {
      ALLOWED_TAGS: options.allowedTags || SECURITY_CONFIG.VALIDATION.allowedHtmlTags,
      ALLOWED_ATTR: options.allowedAttributes || {},
      STRIP_IGNORE_TAG: options.stripIgnoreTag ?? true,
      STRIP_IGNORE_TAG_BODY: options.stripIgnoreTagBody || ['script', 'style'],
      FORBID_TAGS: options.forbiddenTags || ['script', 'object', 'embed', 'form', 'input'],
      FORBID_ATTR: options.forbiddenAttributes || ['onerror', 'onload', 'onclick', 'onmouseover'],
      USE_PROFILES: { html: true },
      SANITIZE_DOM: true,
      KEEP_CONTENT: false,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_TRUSTED_TYPE: false,
    };

    try {
      const clean = this.purify.sanitize(dirty, config);
      
      // Additional validation
      if (this.containsSuspiciousPatterns(clean)) {
        console.warn('XSS Protection: Suspicious patterns detected after sanitization');
        return this.stripAllHTML(dirty);
      }
      
      return clean;
    } catch (error) {
      console.error('XSS Protection: Sanitization failed:', error);
      return this.stripAllHTML(dirty);
    }
  }

  /**
   * Sanitize text content (remove all HTML)
   */
  sanitizeText(input: string): string {
    if (typeof input !== 'string') {
      return String(input);
    }

    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&[#\w]+;/g, '') // Remove HTML entities
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/data:/gi, '') // Remove data: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .trim();
  }

  /**
   * Sanitize URL
   */
  sanitizeURL(url: string): string {
    if (typeof url !== 'string') {
      return '';
    }

    // Remove dangerous protocols
    const dangerousProtocols = [
      'javascript:',
      'data:',
      'vbscript:',
      'file:',
      'ftp:',
    ];

    const lowerUrl = url.toLowerCase();
    for (const protocol of dangerousProtocols) {
      if (lowerUrl.startsWith(protocol)) {
        console.warn('XSS Protection: Blocked dangerous URL protocol:', protocol);
        return '';
      }
    }

    // Allow only http, https, mailto, tel
    const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
    const hasProtocol = /^[a-z][a-z0-9+.-]*:/i.test(url);
    
    if (hasProtocol) {
      const protocol = url.split(':')[0].toLowerCase() + ':';
      if (!allowedProtocols.includes(protocol)) {
        console.warn('XSS Protection: Blocked disallowed URL protocol:', protocol);
        return '';
      }
    }

    // Encode special characters
    return encodeURI(url);
  }

  /**
   * Sanitize CSS
   */
  sanitizeCSS(css: string): string {
    if (typeof css !== 'string') {
      return '';
    }

    // Remove dangerous CSS properties and values
    const dangerousPatterns = [
      /expression\s*\(/gi, // IE expression()
      /javascript\s*:/gi, // javascript: protocol
      /vbscript\s*:/gi, // vbscript: protocol
      /data\s*:/gi, // data: protocol
      /@import/gi, // @import rules
      /binding\s*:/gi, // IE binding
      /behavior\s*:/gi, // IE behavior
      /mozbinding/gi, // Mozilla binding
    ];

    let sanitized = css;
    dangerousPatterns.forEach(pattern => {
      if (pattern.test(sanitized)) {
        console.warn('XSS Protection: Removed dangerous CSS pattern:', pattern);
        sanitized = sanitized.replace(pattern, '');
      }
    });

    return sanitized;
  }

  /**
   * Sanitize JSON data
   */
  sanitizeJSON(data: any): any {
    if (typeof data === 'string') {
      return this.sanitizeText(data);
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeJSON(item));
    }
    
    if (data && typeof data === 'object') {
      const sanitized: any = {};
      Object.keys(data).forEach(key => {
        const sanitizedKey = this.sanitizeText(key);
        sanitized[sanitizedKey] = this.sanitizeJSON(data[key]);
      });
      return sanitized;
    }
    
    return data;
  }

  /**
   * Validate and sanitize form data
   */
  sanitizeFormData(formData: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    Object.entries(formData).forEach(([key, value]) => {
      const sanitizedKey = this.sanitizeText(key);
      
      if (typeof value === 'string') {
        // Check if field should contain HTML
        const htmlFields = ['content', 'description', 'body', 'message'];
        const isHtmlField = htmlFields.some(field => 
          sanitizedKey.toLowerCase().includes(field)
        );
        
        sanitized[sanitizedKey] = isHtmlField 
          ? this.sanitizeHTML(value)
          : this.sanitizeText(value);
      } else {
        sanitized[sanitizedKey] = this.sanitizeJSON(value);
      }
    });
    
    return sanitized;
  }

  /**
   * Check for suspicious patterns
   */
  private containsSuspiciousPatterns(content: string): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /on\w+\s*=/i, // Event handlers
      /expression\s*\(/i,
      /import\s+/i,
      /eval\s*\(/i,
      /setTimeout\s*\(/i,
      /setInterval\s*\(/i,
      /Function\s*\(/i,
      /alert\s*\(/i,
      /confirm\s*\(/i,
      /prompt\s*\(/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Strip all HTML tags
   */
  private stripAllHTML(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  /**
   * Encode HTML entities
   */
  encodeHTML(input: string): string {
    const entityMap: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };

    return String(input).replace(/[&<>"'\/]/g, (char) => entityMap[char]);
  }

  /**
   * Decode HTML entities
   */
  decodeHTML(input: string): string {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = input;
    return textarea.value;
  }

  /**
   * Validate file upload
   */
  validateFileUpload(file: File): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    // Check file size
    if (file.size > SECURITY_CONFIG.FILE_UPLOAD.maxSize) {
      errors.push(`File size exceeds maximum allowed size of ${SECURITY_CONFIG.FILE_UPLOAD.maxSize} bytes`);
    }
    
    // Check file type
    if (!SECURITY_CONFIG.FILE_UPLOAD.allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }
    
    // Check file extension
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SECURITY_CONFIG.FILE_UPLOAD.allowedExtensions.includes(extension)) {
      errors.push(`File extension ${extension} is not allowed`);
    }
    
    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.php$/i,
      /\.asp$/i,
      /\.jsp$/i,
      /\.exe$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.scr$/i,
      /\.vbs$/i,
      /\.js$/i,
    ];
    
    if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
      errors.push('File name contains suspicious patterns');
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Generate security report
   */
  generateSecurityReport(content: string): {
    originalLength: number;
    sanitizedLength: number;
    removedElements: number;
    suspiciousPatterns: boolean;
    timestamp: string;
  } {
    const original = content;
    const sanitized = this.sanitizeHTML(content);
    
    return {
      originalLength: original.length,
      sanitizedLength: sanitized.length,
      removedElements: original.length - sanitized.length,
      suspiciousPatterns: this.containsSuspiciousPatterns(original),
      timestamp: new Date().toISOString(),
    };
  }
}

// Export singleton instance
export const xssProtection = new XSSProtection();

// Global sanitization functions
export const sanitize = {
  html: (input: string, options?: SanitizeOptions) => xssProtection.sanitizeHTML(input, options),
  text: (input: string) => xssProtection.sanitizeText(input),
  url: (input: string) => xssProtection.sanitizeURL(input),
  css: (input: string) => xssProtection.sanitizeCSS(input),
  json: (input: any) => xssProtection.sanitizeJSON(input),
  form: (input: Record<string, any>) => xssProtection.sanitizeFormData(input),
};

export default xssProtection;
