/**
 * CSRF Protection Implementation
 * Cross-Site Request Forgery protection with token management
 */

import { secureStorage } from '../../utils/secureStorage';
import { SecurityUtils, SECURITY_CONFIG } from '../../config/security';
import { loggerHelpers } from '../../store/middleware/loggingMiddleware';

interface CSRFTokenInfo {
  token: string;
  expires: number;
  created: number;
}

class CSRFProtection {
  private tokenKey = 'csrf_token';
  private headerName = SECURITY_CONFIG.CSRF.headerName;
  private cookieName = SECURITY_CONFIG.CSRF.cookieName;
  private tokenLifetime = 60 * 60 * 1000; // 1 hour

  /**
   * Generate a new CSRF token
   */
  generateToken(): string {
    const token = SecurityUtils.generateSecureToken(32);
    const tokenInfo: CSRFTokenInfo = {
      token,
      expires: Date.now() + this.tokenLifetime,
      created: Date.now(),
    };

    // Store token securely
    secureStorage.setSessionItem(this.tokenKey, tokenInfo, true);
    
    // Set cookie for server-side validation
    this.setCookie(token);
    
    loggerHelpers.debug('CSRF token generated', { tokenLength: token.length });
    
    return token;
  }

  /**
   * Get current CSRF token
   */
  getToken(): string | null {
    const tokenInfo = secureStorage.getSessionItem<CSRFTokenInfo>(this.tokenKey, true);
    
    if (!tokenInfo) {
      return this.generateToken();
    }

    // Check if token is expired
    if (Date.now() > tokenInfo.expires) {
      loggerHelpers.debug('CSRF token expired, generating new one');
      return this.generateToken();
    }

    return tokenInfo.token;
  }

  /**
   * Validate CSRF token
   */
  validateToken(token: string): boolean {
    const storedTokenInfo = secureStorage.getSessionItem<CSRFTokenInfo>(this.tokenKey, true);
    
    if (!storedTokenInfo) {
      loggerHelpers.warn('CSRF validation failed: No stored token');
      return false;
    }

    // Check expiration
    if (Date.now() > storedTokenInfo.expires) {
      loggerHelpers.warn('CSRF validation failed: Token expired');
      this.clearToken();
      return false;
    }

    // Compare tokens
    if (storedTokenInfo.token !== token) {
      loggerHelpers.warn('CSRF validation failed: Token mismatch');
      return false;
    }

    loggerHelpers.debug('CSRF token validated successfully');
    return true;
  }

  /**
   * Clear CSRF token
   */
  clearToken(): void {
    secureStorage.removeSessionItem(this.tokenKey);
    this.removeCookie();
    loggerHelpers.debug('CSRF token cleared');
  }

  /**
   * Refresh CSRF token
   */
  refreshToken(): string {
    this.clearToken();
    return this.generateToken();
  }

  /**
   * Add CSRF token to request headers
   */
  addTokenToHeaders(headers: Record<string, string> = {}): Record<string, string> {
    const token = this.getToken();
    if (token) {
      headers[this.headerName] = token;
    }
    return headers;
  }

  /**
   * Add CSRF token to form data
   */
  addTokenToFormData(formData: FormData): FormData {
    const token = this.getToken();
    if (token) {
      formData.append('_csrf', token);
    }
    return formData;
  }

  /**
   * Add CSRF token to URL parameters
   */
  addTokenToURL(url: string): string {
    const token = this.getToken();
    if (!token) return url;

    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}_csrf=${encodeURIComponent(token)}`;
  }

  /**
   * Create CSRF-protected form
   */
  protectForm(form: HTMLFormElement): void {
    const token = this.getToken();
    if (!token) return;

    // Remove existing CSRF input
    const existingInput = form.querySelector('input[name="_csrf"]');
    if (existingInput) {
      existingInput.remove();
    }

    // Add new CSRF input
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_csrf';
    csrfInput.value = token;
    form.appendChild(csrfInput);

    loggerHelpers.debug('Form protected with CSRF token');
  }

  /**
   * Protect all forms on the page
   */
  protectAllForms(): void {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => this.protectForm(form));
    loggerHelpers.debug(`Protected ${forms.length} forms with CSRF tokens`);
  }

  /**
   * Set CSRF cookie
   */
  private setCookie(token: string): void {
    const expires = new Date(Date.now() + this.tokenLifetime);
    const cookieValue = `${this.cookieName}=${token}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
    
    try {
      document.cookie = cookieValue;
    } catch (error) {
      loggerHelpers.error('Failed to set CSRF cookie', error);
    }
  }

  /**
   * Remove CSRF cookie
   */
  private removeCookie(): void {
    try {
      document.cookie = `${this.cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    } catch (error) {
      loggerHelpers.error('Failed to remove CSRF cookie', error);
    }
  }

  /**
   * Check if request needs CSRF protection
   */
  needsProtection(method: string, url: string): boolean {
    // Only protect state-changing methods
    const protectedMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    if (!protectedMethods.includes(method.toUpperCase())) {
      return false;
    }

    // Skip protection for certain URLs
    const skipUrls = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
    ];

    return !skipUrls.some(skipUrl => url.includes(skipUrl));
  }

  /**
   * Validate request origin
   */
  validateOrigin(origin: string, referer?: string): boolean {
    const allowedOrigins = SECURITY_CONFIG.API.corsOrigins;
    
    // Check origin
    if (origin && !allowedOrigins.includes(origin)) {
      loggerHelpers.warn('CSRF validation failed: Invalid origin', { origin });
      return false;
    }

    // Check referer as fallback
    if (!origin && referer) {
      const refererOrigin = new URL(referer).origin;
      if (!allowedOrigins.includes(refererOrigin)) {
        loggerHelpers.warn('CSRF validation failed: Invalid referer', { referer });
        return false;
      }
    }

    return true;
  }

  /**
   * Create CSRF middleware for fetch requests
   */
  createFetchMiddleware() {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const request = new Request(input, init);
      const method = request.method;
      const url = request.url;

      // Add CSRF token if needed
      if (this.needsProtection(method, url)) {
        const token = this.getToken();
        if (token) {
          request.headers.set(this.headerName, token);
        }
      }

      return originalFetch(request);
    };
  }

  /**
   * Create CSRF middleware for XMLHttpRequest
   */
  createXHRMiddleware(): void {
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      this._method = method;
      this._url = url.toString();
      return originalOpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function(body?: any) {
      if (this._method && this._url && csrfProtection.needsProtection(this._method, this._url)) {
        const token = csrfProtection.getToken();
        if (token) {
          this.setRequestHeader(csrfProtection.headerName, token);
        }
      }
      return originalSend.apply(this, [body]);
    };
  }

  /**
   * Initialize CSRF protection
   */
  initialize(): void {
    // Generate initial token
    this.generateToken();

    // Set up middlewares
    this.createFetchMiddleware();
    this.createXHRMiddleware();

    // Protect existing forms
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.protectAllForms();
      });
    } else {
      this.protectAllForms();
    }

    // Monitor for new forms
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            
            // Check if the added node is a form
            if (element.tagName === 'FORM') {
              this.protectForm(element as HTMLFormElement);
            }
            
            // Check for forms within the added node
            const forms = element.querySelectorAll('form');
            forms.forEach(form => this.protectForm(form));
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Refresh token periodically
    setInterval(() => {
      const tokenInfo = secureStorage.getSessionItem<CSRFTokenInfo>(this.tokenKey, true);
      if (tokenInfo && Date.now() > tokenInfo.expires - (5 * 60 * 1000)) { // 5 minutes before expiry
        this.refreshToken();
        loggerHelpers.debug('CSRF token refreshed automatically');
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    loggerHelpers.info('CSRF protection initialized');
  }

  /**
   * Get protection status
   */
  getStatus(): {
    hasToken: boolean;
    tokenExpiry: number | null;
    tokenAge: number | null;
    formsProtected: number;
  } {
    const tokenInfo = secureStorage.getSessionItem<CSRFTokenInfo>(this.tokenKey, true);
    const forms = document.querySelectorAll('form input[name="_csrf"]');

    return {
      hasToken: !!tokenInfo,
      tokenExpiry: tokenInfo?.expires || null,
      tokenAge: tokenInfo ? Date.now() - tokenInfo.created : null,
      formsProtected: forms.length,
    };
  }
}

// Export singleton instance
export const csrfProtection = new CSRFProtection();

// Auto-initialize on load
if (typeof window !== 'undefined') {
  csrfProtection.initialize();
}

export default csrfProtection;
