/**
 * useHttp Hook
 * Type-safe HTTP client with automatic error handling, retries, and loading states
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { httpClient } from '../services/httpClient';
import { addNotification } from '../store/slices/notificationSlice';
import { addSecurityEvent } from '../store/slices/auditSlice';
import { loggerHelpers } from '../store/middleware/loggingMiddleware';

interface UseHttpState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

interface UseHttpOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
  retries?: number;
  retryDelay?: number;
  timeout?: number;
  showErrorNotification?: boolean;
  showSuccessNotification?: boolean;
  successMessage?: string;
}

interface UseHttpMethods<T> {
  execute: (params?: any) => Promise<T>;
  reset: () => void;
  cancel: () => void;
}

type UseHttpReturn<T> = UseHttpState<T> & UseHttpMethods<T>;

/**
 * Main useHttp hook for API requests
 */
export function useHttp<T = any>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  options: UseHttpOptions = {}
): UseHttpReturn<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    retries = 3,
    retryDelay = 1000,
    timeout = 30000,
    showErrorNotification = true,
    showSuccessNotification = false,
    successMessage = 'Operation completed successfully',
  } = options;

  const [state, setState] = useState<UseHttpState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const dispatch = useDispatch();
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryCountRef = useRef(0);

  const execute = useCallback(async (params?: any): Promise<T> => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setState(prev => ({ ...prev, loading: true, error: null }));
    retryCountRef.current = 0;

    const attemptRequest = async (): Promise<T> => {
      try {
        loggerHelpers.debug(`HTTP Request: ${method} ${endpoint}`, params);

        const response = await httpClient.request<T>({
          url: endpoint,
          method,
          data: params,
          signal: abortControllerRef.current?.signal,
          timeout,
        });

        const data = response.data;
        
        setState(prev => ({ ...prev, data, loading: false, error: null }));
        
        // Success callback
        if (onSuccess) {
          onSuccess(data);
        }
        
        // Success notification
        if (showSuccessNotification) {
          dispatch(addNotification({
            id: Date.now().toString(),
            type: 'success',
            title: 'Success',
            message: successMessage,
            read: false,
            createdAt: new Date().toISOString(),
          }));
        }

        loggerHelpers.info(`HTTP Success: ${method} ${endpoint}`, { data });
        
        return data;
      } catch (error: any) {
        // Don't handle aborted requests
        if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
          throw error;
        }

        const apiError: ApiError = {
          message: error.response?.data?.message || error.message || 'Request failed',
          code: error.response?.data?.code || error.code || 'UNKNOWN_ERROR',
          status: error.response?.status || 500,
          details: error.response?.data?.details || {},
          timestamp: new Date().toISOString(),
        };

        loggerHelpers.error(`HTTP Error: ${method} ${endpoint}`, apiError);

        // Retry logic
        if (retryCountRef.current < retries && shouldRetry(apiError)) {
          retryCountRef.current++;
          
          loggerHelpers.info(`Retrying request (${retryCountRef.current}/${retries}): ${method} ${endpoint}`);
          
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, retryDelay * retryCountRef.current));
          
          return attemptRequest();
        }

        // Set error state
        setState(prev => ({ ...prev, loading: false, error: apiError }));

        // Error callback
        if (onError) {
          onError(apiError);
        }

        // Error notification
        if (showErrorNotification) {
          dispatch(addNotification({
            id: Date.now().toString(),
            type: 'error',
            title: 'Request Failed',
            message: apiError.message,
            read: false,
            createdAt: new Date().toISOString(),
          }));
        }

        // Log security event for certain errors
        if (apiError.status === 401 || apiError.status === 403) {
          dispatch(addSecurityEvent({
            id: Date.now().toString(),
            type: 'permission_denied',
            ip: 'client-side',
            userAgent: navigator.userAgent,
            details: {
              endpoint,
              method,
              status: apiError.status,
              error: apiError.message,
            },
            timestamp: new Date().toISOString(),
            severity: 'medium',
          }));
        }

        throw apiError;
      }
    };

    return attemptRequest();
  }, [endpoint, method, onSuccess, onError, retries, retryDelay, timeout, showErrorNotification, showSuccessNotification, successMessage, dispatch]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
    retryCountRef.current = 0;
  }, []);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setState(prev => ({ ...prev, loading: false }));
    }
  }, []);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute().catch(() => {
        // Error already handled in execute function
      });
    }
  }, [immediate, execute]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    execute,
    reset,
    cancel,
  };
}

/**
 * Specialized hooks for common HTTP methods
 */
export function useGet<T = any>(endpoint: string, options?: UseHttpOptions): UseHttpReturn<T> {
  return useHttp<T>(endpoint, 'GET', options);
}

export function usePost<T = any>(endpoint: string, options?: UseHttpOptions): UseHttpReturn<T> {
  return useHttp<T>(endpoint, 'POST', options);
}

export function usePut<T = any>(endpoint: string, options?: UseHttpOptions): UseHttpReturn<T> {
  return useHttp<T>(endpoint, 'PUT', options);
}

export function useDelete<T = any>(endpoint: string, options?: UseHttpOptions): UseHttpReturn<T> {
  return useHttp<T>(endpoint, 'DELETE', options);
}

export function usePatch<T = any>(endpoint: string, options?: UseHttpOptions): UseHttpReturn<T> {
  return useHttp<T>(endpoint, 'PATCH', options);
}

/**
 * Hook for paginated data
 */
export function usePaginatedHttp<T = any>(
  endpoint: string,
  initialParams: SearchParams = {}
) {
  const [params, setParams] = useState<SearchParams>(initialParams);
  
  const { data, loading, error, execute, reset } = usePost<{
    data: T[];
    meta: { pagination: PaginationMeta };
  }>(endpoint, {
    immediate: true,
  });

  const loadPage = useCallback((page: number) => {
    const newParams = { ...params, page };
    setParams(newParams);
    return execute(newParams);
  }, [params, execute]);

  const changePageSize = useCallback((limit: number) => {
    const newParams = { ...params, limit, page: 1 };
    setParams(newParams);
    return execute(newParams);
  }, [params, execute]);

  const applyFilters = useCallback((filters: Record<string, any>) => {
    const newParams = { ...params, filters, page: 1 };
    setParams(newParams);
    return execute(newParams);
  }, [params, execute]);

  const applySort = useCallback((field: string, order: 'asc' | 'desc') => {
    const newParams = { ...params, sort: { field, order }, page: 1 };
    setParams(newParams);
    return execute(newParams);
  }, [params, execute]);

  const search = useCallback((query: string) => {
    const newParams = { ...params, query, page: 1 };
    setParams(newParams);
    return execute(newParams);
  }, [params, execute]);

  const refresh = useCallback(() => {
    return execute(params);
  }, [params, execute]);

  return {
    data: data?.data || [],
    pagination: data?.meta?.pagination,
    loading,
    error,
    params,
    loadPage,
    changePageSize,
    applyFilters,
    applySort,
    search,
    refresh,
    reset,
  };
}

/**
 * Hook for file uploads
 */
export function useFileUpload(endpoint: string = '/upload') {
  const [uploadProgress, setUploadProgress] = useState(0);
  
  const { loading, error, execute } = usePost<FileUpload>(endpoint, {
    showSuccessNotification: true,
    successMessage: 'File uploaded successfully',
  });

  const upload = useCallback(async (file: File, additionalData?: Record<string, any>) => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    setUploadProgress(0);

    try {
      const result = await httpClient.request<FileUpload>({
        url: endpoint,
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(progress);
          }
        },
      });

      setUploadProgress(100);
      return result.data;
    } catch (error) {
      setUploadProgress(0);
      throw error;
    }
  }, [endpoint]);

  return {
    upload,
    loading,
    error,
    uploadProgress,
  };
}

/**
 * Utility function to determine if request should be retried
 */
function shouldRetry(error: ApiError): boolean {
  // Don't retry client errors (4xx) except for specific cases
  if (error.status >= 400 && error.status < 500) {
    // Retry on rate limiting or temporary auth issues
    return error.status === 429 || error.status === 408;
  }
  
  // Retry server errors (5xx) and network errors
  return error.status >= 500 || error.status === 0;
}
