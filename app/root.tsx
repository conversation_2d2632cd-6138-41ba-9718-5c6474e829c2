import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "react-router";

import type { Route } from "./+types/root";
import "./app.css";
import { RBACErrorDisplay } from "./lib/rbac/components/ErrorBoundary";

export const links: Route.LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let statusCode = 500;
  let title = "Oops!";
  let subtitle = "An unexpected error occurred.";

  if (isRouteErrorResponse(error)) {
    statusCode = error.status;
    title = error.status === 404 ? "404" : "Error";
    subtitle =
      error.status === 404
        ? "The requested page could not be found."
        : error.statusText || subtitle;
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    subtitle = error.message;
  }

  // Use RBAC error display for better UX
  return (
    <RBACErrorDisplay
      error={error instanceof Error ? error : undefined}
      statusCode={statusCode}
      title={title}
      subtitle={subtitle}
      showHomeButton={true}
      showRetryButton={false}
    />
  );
}
