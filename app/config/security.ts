/**
 * Security Configuration
 * Centralized security settings and constants
 */

export const SECURITY_CONFIG = {
  // Authentication
  TOKEN_EXPIRY: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60 * 1000, // 7 days
  SESSION_TIMEOUT: parseInt(import.meta.env.VITE_SESSION_TIMEOUT) || 60 * 60 * 1000, // 1 hour
  MAX_LOGIN_ATTEMPTS: parseInt(import.meta.env.VITE_MAX_LOGIN_ATTEMPTS) || 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  
  // Password Policy
  PASSWORD_POLICY: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    preventReuse: 5,
    expirationDays: 90,
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  },
  
  // Rate Limiting
  RATE_LIMIT: {
    windowMs: parseInt(import.meta.env.VITE_RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(import.meta.env.VITE_RATE_LIMIT_MAX) || 100, // requests per window
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },
  
  // CSRF Protection
  CSRF: {
    secret: import.meta.env.VITE_CSRF_SECRET || 'default-csrf-secret',
    cookieName: '__Host-csrf-token',
    headerName: 'X-CSRF-Token',
  },
  
  // Content Security Policy
  CSP: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.example.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      manifestSrc: ["'self'"],
      workerSrc: ["'self'"],
    },
  },
  
  // Security Headers
  HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  },
  
  // File Upload
  FILE_UPLOAD: {
    maxSize: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.doc', '.docx', '.xls', '.xlsx'],
  },
  
  // Encryption
  ENCRYPTION: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
    iterations: 100000,
  },
  
  // Audit Logging
  AUDIT: {
    enabled: import.meta.env.VITE_ENABLE_AUDIT_LOGS === 'true',
    retentionDays: 90,
    sensitiveFields: ['password', 'token', 'secret', 'key'],
    events: {
      LOGIN: 'login',
      LOGOUT: 'logout',
      CREATE: 'create',
      UPDATE: 'update',
      DELETE: 'delete',
      VIEW: 'view',
      EXPORT: 'export',
      IMPORT: 'import',
      PERMISSION_CHANGE: 'permission_change',
      SETTINGS_CHANGE: 'settings_change',
    },
  },
  
  // Session Management
  SESSION: {
    cookieName: '__Host-session',
    cookieOptions: {
      httpOnly: true,
      secure: true,
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
    storage: {
      prefix: 'admin_panel_',
      encryptionKey: import.meta.env.VITE_ENCRYPTION_KEY || 'default-encryption-key',
    },
  },
  
  // API Security
  API: {
    timeout: 30000, // 30 seconds
    retries: 3,
    retryDelay: 1000, // 1 second
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
    corsOrigins: ['http://localhost:3000', 'https://yourdomain.com'],
  },
  
  // Input Validation
  VALIDATION: {
    maxStringLength: 1000,
    maxArrayLength: 100,
    maxObjectDepth: 10,
    allowedHtmlTags: ['b', 'i', 'u', 'strong', 'em', 'p', 'br', 'ul', 'ol', 'li'],
    sanitizeOptions: {
      allowedTags: ['b', 'i', 'u', 'strong', 'em', 'p', 'br', 'ul', 'ol', 'li'],
      allowedAttributes: {},
      disallowedTagsMode: 'discard',
    },
  },
} as const;

// Security utility functions
export const SecurityUtils = {
  /**
   * Generate a secure random string
   */
  generateSecureToken: (length: number = 32): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const cryptoObj = window.crypto || (window as any).msCrypto;
    
    if (cryptoObj && cryptoObj.getRandomValues) {
      const randomArray = new Uint8Array(length);
      cryptoObj.getRandomValues(randomArray);
      
      for (let i = 0; i < length; i++) {
        result += chars[randomArray[i] % chars.length];
      }
    } else {
      // Fallback for older browsers
      for (let i = 0; i < length; i++) {
        result += chars[Math.floor(Math.random() * chars.length)];
      }
    }
    
    return result;
  },
  
  /**
   * Validate password strength
   */
  validatePassword: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const policy = SECURITY_CONFIG.PASSWORD_POLICY;
    
    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }
    
    if (password.length > policy.maxLength) {
      errors.push(`Password must not exceed ${policy.maxLength} characters`);
    }
    
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (policy.requireSpecialChars && !new RegExp(`[${policy.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  },
  
  /**
   * Calculate password strength score
   */
  calculatePasswordStrength: (password: string): { score: number; level: string } => {
    let score = 0;
    
    // Length bonus
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;
    
    // Character variety
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[^a-zA-Z\d]/.test(password)) score += 1;
    
    // Patterns
    if (!/(.)\1{2,}/.test(password)) score += 1; // No repeated characters
    if (!/123|abc|qwe/i.test(password)) score += 1; // No common sequences
    
    const levels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
    const level = levels[Math.min(score, levels.length - 1)];
    
    return { score, level };
  },
  
  /**
   * Sanitize user input
   */
  sanitizeInput: (input: string): string => {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  },
  
  /**
   * Check if origin is allowed
   */
  isOriginAllowed: (origin: string): boolean => {
    return SECURITY_CONFIG.API.corsOrigins.includes(origin);
  },
  
  /**
   * Generate CSRF token
   */
  generateCSRFToken: (): string => {
    return SecurityUtils.generateSecureToken(32);
  },
};
