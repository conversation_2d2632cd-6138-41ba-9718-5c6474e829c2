/**
 * Application Configuration
 * Main app settings and constants
 */

export const APP_CONFIG = {
  // Application Info
  name: import.meta.env.VITE_APP_NAME || 'Admin Panel',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  description: 'Production-ready admin panel with comprehensive security',
  
  // API Configuration
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
  },
  
  // Route Configuration with Security Paths
  routes: {
    // Public routes
    home: '/',
    login: '/fghasfdfanbrmna', // Obfuscated login path
    register: '/Xdrthawwr/register/ghd', // Obfuscated register path
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    
    // Protected routes with obfuscated paths
    dashboard: '/admin/dashboard',
    users: '/admin/users',
    roles: '/admin/roles',
    settings: '/admin/settings',
    profile: '/admin/profile',
    audit: '/admin/audit-logs',
    
    // Super admin routes (highly obfuscated)
    superAdmin: '/sa/xk9m2p7q',
    systemSettings: '/sa/xk9m2p7q/system',
    securityLogs: '/sa/xk9m2p7q/security',
    
    // API routes
    api: {
      auth: '/auth',
      users: '/users',
      roles: '/roles',
      permissions: '/permissions',
      audit: '/audit',
      settings: '/settings',
      upload: '/upload',
    },
  },
  
  // UI Configuration
  ui: {
    theme: {
      default: 'light',
      primary: '#1890ff',
      borderRadius: 6,
      fontSize: 14,
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    },
    layout: {
      sidebarWidth: 250,
      sidebarCollapsedWidth: 80,
      headerHeight: 64,
      footerHeight: 48,
    },
    table: {
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '50', '100'],
      showSizeChanger: true,
      showQuickJumper: true,
    },
    form: {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      validateTrigger: 'onBlur',
    },
  },
  
  // Feature Flags
  features: {
    twoFactorAuth: import.meta.env.VITE_ENABLE_TWO_FACTOR === 'true',
    emailVerification: import.meta.env.VITE_ENABLE_EMAIL_VERIFICATION === 'true',
    auditLogs: import.meta.env.VITE_ENABLE_AUDIT_LOGS === 'true',
    darkMode: true,
    multiLanguage: true,
    fileUpload: true,
    richTextEditor: true,
    inlineEditing: true,
    bulkOperations: true,
    dataExport: true,
    realTimeNotifications: true,
    advancedSearch: true,
    customDashboard: true,
  },
  
  // Pagination & Search
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true,
  },
  
  search: {
    debounceDelay: 300,
    minQueryLength: 2,
    maxResults: 1000,
    highlightMatches: true,
  },
  
  // File Upload
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    uploadPath: '/uploads',
    thumbnailSizes: [150, 300, 600],
  },
  
  // Rich Text Editor
  editor: {
    toolbar: [
      'heading', 'bold', 'italic', 'underline', 'strikethrough',
      'link', 'bulletedList', 'numberedList', 'blockQuote',
      'insertTable', 'imageUpload', 'mediaEmbed',
      'undo', 'redo', 'findAndReplace', 'selectAll',
    ],
    plugins: [
      'Essentials', 'Paragraph', 'Heading', 'Bold', 'Italic', 'Underline',
      'Strikethrough', 'Link', 'List', 'BlockQuote', 'Table', 'Image',
      'MediaEmbed', 'Undo', 'FindAndReplace', 'SelectAll',
    ],
    maxLength: 50000,
    autosave: {
      enabled: true,
      interval: 30000, // 30 seconds
    },
  },
  
  // Internationalization
  i18n: {
    defaultLanguage: 'en',
    fallbackLanguage: 'en',
    supportedLanguages: [
      { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
      { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
      { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
      { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
      { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹' },
      { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹' },
      { code: 'ru', name: 'Russian', nativeName: 'Русский', flag: '🇷🇺' },
      { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
      { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
      { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷' },
    ],
    namespaces: ['common', 'auth', 'dashboard', 'users', 'settings', 'errors'],
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  
  // Dashboard Widgets
  dashboard: {
    refreshInterval: 30000, // 30 seconds
    widgets: {
      stats: { enabled: true, refreshInterval: 60000 },
      charts: { enabled: true, refreshInterval: 300000 },
      recentActivity: { enabled: true, refreshInterval: 30000 },
      notifications: { enabled: true, refreshInterval: 15000 },
      quickActions: { enabled: true },
      systemHealth: { enabled: true, refreshInterval: 60000 },
    },
    layout: {
      breakpoints: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
      cols: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
      rowHeight: 60,
      margin: [10, 10],
      containerPadding: [10, 10],
    },
  },
  
  // Notification Settings
  notifications: {
    position: 'topRight',
    duration: 4.5,
    maxCount: 5,
    rtl: false,
    types: {
      success: { icon: '✓', color: '#52c41a' },
      error: { icon: '✗', color: '#ff4d4f' },
      warning: { icon: '⚠', color: '#faad14' },
      info: { icon: 'ℹ', color: '#1890ff' },
    },
  },
  
  // Development Settings
  development: {
    debug: import.meta.env.VITE_DEBUG === 'true',
    mockApi: import.meta.env.NODE_ENV === 'development',
    showReduxDevTools: import.meta.env.NODE_ENV === 'development',
    logLevel: import.meta.env.NODE_ENV === 'development' ? 'debug' : 'error',
  },
  
  // Performance Settings
  performance: {
    lazyLoading: true,
    virtualScrolling: true,
    imageOptimization: true,
    caching: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100, // Max cached items
    },
    debounce: {
      search: 300,
      resize: 100,
      scroll: 50,
    },
  },
} as const;

// Environment-specific overrides
if (import.meta.env.NODE_ENV === 'production') {
  // Production optimizations
  Object.assign(APP_CONFIG.performance, {
    caching: {
      enabled: true,
      ttl: 600000, // 10 minutes in production
      maxSize: 200,
    },
  });
}

export default APP_CONFIG;
