<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .offline-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            margin: 2rem;
        }

        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: #f0f2f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .offline-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .offline-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f0f2f5;
            color: #1f2937;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }

        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-offline {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .status-online {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features-list {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
        }

        .features-list h3 {
            margin-bottom: 1rem;
            color: #1f2937;
            font-size: 1.1rem;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 0.5rem 0;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .features-list li::before {
            content: "✓";
            color: #16a34a;
            font-weight: bold;
        }

        @media (max-width: 640px) {
            .offline-container {
                padding: 2rem;
                margin: 1rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - you can still access some features of the admin panel while offline.
        </p>

        <div class="features-list">
            <h3>Available Offline:</h3>
            <ul>
                <li>View cached dashboard data</li>
                <li>Browse previously loaded content</li>
                <li>Access user interface</li>
                <li>View system settings</li>
            </ul>
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="tryReconnect()">
                <span class="loading-spinner" id="reconnectSpinner" style="display: none;"></span>
                <span id="reconnectText">Try Again</span>
            </button>
            
            <a href="/" class="btn btn-secondary">
                Go to Dashboard
            </a>
        </div>

        <div class="connection-status status-offline" id="connectionStatus">
            🔴 No internet connection
        </div>
    </div>

    <script>
        let isReconnecting = false;

        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '🟢 Connected to internet';
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '🔴 No internet connection';
            }
        }

        // Try to reconnect
        async function tryReconnect() {
            if (isReconnecting) return;
            
            isReconnecting = true;
            const spinner = document.getElementById('reconnectSpinner');
            const text = document.getElementById('reconnectText');
            
            spinner.style.display = 'inline-block';
            text.textContent = 'Connecting...';
            
            try {
                // Try to fetch a small resource to test connectivity
                const response = await fetch('/', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    // Connection restored, reload the page
                    window.location.reload();
                } else {
                    throw new Error('Connection test failed');
                }
            } catch (error) {
                console.log('Still offline:', error);
                
                // Show feedback
                text.textContent = 'Still offline';
                setTimeout(() => {
                    text.textContent = 'Try Again';
                    spinner.style.display = 'none';
                    isReconnecting = false;
                }, 2000);
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus();
            // Auto-reload when connection is restored
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        window.addEventListener('offline', updateConnectionStatus);

        // Periodic connection check
        setInterval(() => {
            updateConnectionStatus();
            
            // Auto-retry connection every 30 seconds if offline
            if (!navigator.onLine && !isReconnecting) {
                tryReconnect();
            }
        }, 30000);

        // Initial status check
        updateConnectionStatus();

        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
            });
        }

        // Handle back button
        window.addEventListener('popstate', (event) => {
            // Try to go back to the app
            window.location.href = '/';
        });
    </script>
</body>
</html>
