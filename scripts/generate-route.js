#!/usr/bin/env node

/**
 * Route Generator Script
 * Generates new routes with proper flat structure and boilerplate code
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Route templates
const TEMPLATES = {
  page: `/**
 * {{ROUTE_NAME}} Page
 * {{DESCRIPTION}}
 */

import React from 'react';
import { useLoaderData } from 'react-router';
import { RouteProtection } from '~/lib/rbac/advanced/RouteProtection';
import { HasPermission } from '~/lib/rbac/advanced/PermissionRenderer';
import { Card, Typography, Button, Space } from 'antd';
import { useAdvancedTranslation } from '~/lib/i18n/translationUtils';

const { Title, Paragraph } = Typography;

export default function {{COMPONENT_NAME}}() {
  const { t } = useAdvancedTranslation();
  
  return (
    <RouteProtection
      requiredPermissions={[{ resource: '{{RESOURCE}}', action: 'read' }]}
    >
      <div className="p-6">
        <div className="mb-6">
          <Title level={2}>{{TITLE}}</Title>
          <Paragraph type="secondary">
            {{DESCRIPTION}}
          </Paragraph>
        </div>

        <Card>
          <div className="text-center py-8">
            <Title level={4}>{{ROUTE_NAME}} Content</Title>
            <Paragraph>
              This is the {{ROUTE_NAME}} page. Add your content here.
            </Paragraph>
            
            <Space>
              <HasPermission resource="{{RESOURCE}}" action="create">
                <Button type="primary">
                  {t('common.create')}
                </Button>
              </HasPermission>
              
              <Button>
                {t('common.refresh')}
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    </RouteProtection>
  );
}

// Loader function
export async function loader() {
  // Add your data loading logic here
  return {
    data: null,
  };
}

// Action function (for forms)
export async function action({ request }) {
  // Add your form handling logic here
  return null;
}
`,

  api: `/**
 * {{ROUTE_NAME}} API Route
 * {{DESCRIPTION}}
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from 'react-router';
import { requireAuth } from '~/lib/auth/authHelpers';
import { requirePermission } from '~/lib/rbac/rbacHelpers';
import { auditLogger } from '~/lib/rbac/advanced/AuditLogger';
import { xssProtection } from '~/lib/security/XSSProtection';

// GET handler
export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  await requirePermission(user, '{{RESOURCE}}', 'read');

  try {
    // Add your GET logic here
    const data = {
      message: '{{ROUTE_NAME}} data',
      timestamp: new Date().toISOString(),
    };

    auditLogger.logDataAccess('{{RESOURCE}}', 'read', {
      context: { userId: user.id },
    });

    return json(data);
  } catch (error) {
    console.error('{{ROUTE_NAME}} GET error:', error);
    return json(
      { error: 'Failed to fetch {{ROUTE_NAME}} data' },
      { status: 500 }
    );
  }
}

// POST handler
export async function action({ request }: ActionFunctionArgs) {
  const user = await requireAuth(request);
  await requirePermission(user, '{{RESOURCE}}', 'create');

  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData);
    
    // Sanitize input data
    const sanitizedData = xssProtection.sanitizeFormData(data);

    // Add your POST logic here
    console.log('Creating {{ROUTE_NAME}}:', sanitizedData);

    auditLogger.logUserAction('create', '{{RESOURCE}}', {
      newValues: sanitizedData,
      context: { userId: user.id },
    });

    return json({ success: true, data: sanitizedData });
  } catch (error) {
    console.error('{{ROUTE_NAME}} POST error:', error);
    return json(
      { error: 'Failed to create {{ROUTE_NAME}}' },
      { status: 500 }
    );
  }
}
`,

  form: `/**
 * {{ROUTE_NAME}} Form Page
 * {{DESCRIPTION}}
 */

import React from 'react';
import { Form, Input, Button, Card, Typography, Space, message } from 'antd';
import { useNavigate, useActionData, useNavigation } from 'react-router';
import { RouteProtection } from '~/lib/rbac/advanced/RouteProtection';
import { useAdvancedTranslation } from '~/lib/i18n/translationUtils';

const { Title, Paragraph } = Typography;

export default function {{COMPONENT_NAME}}() {
  const { t } = useAdvancedTranslation();
  const navigate = useNavigate();
  const actionData = useActionData();
  const navigation = useNavigation();
  const [form] = Form.useForm();

  const isSubmitting = navigation.state === 'submitting';

  const handleSubmit = (values) => {
    // Form will be submitted automatically by React Router
    console.log('Form values:', values);
  };

  const handleCancel = () => {
    navigate(-1);
  };

  // Show success message
  React.useEffect(() => {
    if (actionData?.success) {
      message.success(t('common.success'));
      navigate(-1);
    } else if (actionData?.error) {
      message.error(actionData.error);
    }
  }, [actionData, navigate, t]);

  return (
    <RouteProtection
      requiredPermissions={[{ resource: '{{RESOURCE}}', action: 'create' }]}
    >
      <div className="p-6 max-w-2xl mx-auto">
        <div className="mb-6">
          <Title level={2}>{{TITLE}}</Title>
          <Paragraph type="secondary">
            {{DESCRIPTION}}
          </Paragraph>
        </div>

        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            method="post"
          >
            <Form.Item
              name="name"
              label={t('common.name')}
              rules={[
                { required: true, message: t('validation.required') },
                { min: 2, message: t('validation.minLength', { min: 2 }) },
              ]}
            >
              <Input placeholder={t('common.enterName')} />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('common.description')}
              rules={[
                { required: true, message: t('validation.required') },
              ]}
            >
              <Input.TextArea
                rows={4}
                placeholder={t('common.enterDescription')}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isSubmitting}
                >
                  {t('common.save')}
                </Button>
                <Button onClick={handleCancel}>
                  {t('common.cancel')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </RouteProtection>
  );
}

// Action function to handle form submission
export async function action({ request }) {
  const formData = await request.formData();
  const data = Object.fromEntries(formData);

  try {
    // Add your form processing logic here
    console.log('Processing form data:', data);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    return { success: true, data };
  } catch (error) {
    console.error('Form submission error:', error);
    return { error: 'Failed to process form' };
  }
}
`,
};

// Helper functions
function toPascalCase(str) {
  return str
    .split(/[-_+\s]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}

function toKebabCase(str) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .toLowerCase()
    .replace(/[_\s]+/g, '-');
}

function generateFileName(routePath, type) {
  const segments = routePath.split('/').filter(Boolean);
  const fileName = segments.join('+');
  
  if (type === 'api') {
    return `api+${fileName}.ts`;
  }
  
  return `${fileName}.tsx`;
}

function replaceTemplateVars(template, vars) {
  let result = template;
  Object.entries(vars).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value);
  });
  return result;
}

function updateRoutesFile(routePath, fileName) {
  const routesFilePath = path.join(process.cwd(), 'app', 'routes.ts');
  
  if (!fs.existsSync(routesFilePath)) {
    console.log('⚠️  routes.ts not found. Please add the route manually.');
    return;
  }

  const routesContent = fs.readFileSync(routesFilePath, 'utf8');
  const routeEntry = `  route("${routePath}", "routes/${fileName}"),`;
  
  // Find the right place to insert the route
  const lines = routesContent.split('\n');
  const insertIndex = lines.findIndex(line => line.includes('] satisfies RouteConfig;'));
  
  if (insertIndex > 0) {
    lines.splice(insertIndex, 0, routeEntry);
    fs.writeFileSync(routesFilePath, lines.join('\n'));
    console.log('✅ Updated routes.ts');
  } else {
    console.log('⚠️  Could not automatically update routes.ts. Please add manually:');
    console.log(routeEntry);
  }
}

// Main generation function
async function generateRoute() {
  console.log('🚀 Route Generator for Flat Routes\n');

  // Get route type
  const routeType = await new Promise(resolve => {
    rl.question('Route type (page/api/form): ', resolve);
  });

  if (!['page', 'api', 'form'].includes(routeType)) {
    console.log('❌ Invalid route type. Must be: page, api, or form');
    rl.close();
    return;
  }

  // Get route path
  const routePath = await new Promise(resolve => {
    rl.question('Route path (e.g., admin/users/create): ', resolve);
  });

  if (!routePath) {
    console.log('❌ Route path is required');
    rl.close();
    return;
  }

  // Get description
  const description = await new Promise(resolve => {
    rl.question('Description: ', resolve);
  });

  // Get resource name for permissions
  const resource = await new Promise(resolve => {
    rl.question('Resource name for permissions (e.g., users): ', resolve);
  });

  rl.close();

  // Generate file name and component name
  const fileName = generateFileName(routePath, routeType);
  const componentName = toPascalCase(routePath.replace(/[/:]/g, ' ')) + 'Page';
  const routeName = routePath.split('/').pop() || 'Route';
  const title = routeName.charAt(0).toUpperCase() + routeName.slice(1);

  // Template variables
  const templateVars = {
    ROUTE_NAME: routeName,
    COMPONENT_NAME: componentName,
    DESCRIPTION: description || `${title} management page`,
    RESOURCE: resource || routeName.toLowerCase(),
    TITLE: title,
  };

  // Generate file content
  const template = TEMPLATES[routeType];
  const fileContent = replaceTemplateVars(template, templateVars);

  // Create file
  const routesDir = path.join(process.cwd(), 'app', 'routes');
  const filePath = path.join(routesDir, fileName);

  if (!fs.existsSync(routesDir)) {
    fs.mkdirSync(routesDir, { recursive: true });
  }

  if (fs.existsSync(filePath)) {
    console.log('❌ File already exists:', fileName);
    return;
  }

  fs.writeFileSync(filePath, fileContent);
  console.log('✅ Generated route file:', fileName);

  // Update routes.ts
  if (routeType !== 'api') {
    updateRoutesFile(routePath, fileName);
  }

  console.log('\n🎉 Route generated successfully!');
  console.log(`📁 File: app/routes/${fileName}`);
  console.log(`🌐 URL: /${routePath}`);
  console.log(`🔧 Component: ${componentName}`);
  
  if (routeType === 'api') {
    console.log('\n⚠️  Don\'t forget to add the API route to routes.ts manually!');
  }
}

// Run the generator
if (require.main === module) {
  generateRoute().catch(console.error);
}

module.exports = { generateRoute };
