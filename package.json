{"name": "admin-panel-cf", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-router/node": "^7.7.0", "@react-router/serve": "^7.7.0", "@reduxjs/toolkit": "^2.8.2", "@types/dompurify": "^3.2.0", "@types/js-cookie": "^3.0.6", "antd": "^5.26.6", "axios": "^1.10.0", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "helmet": "^8.1.0", "i18next": "^25.3.2", "isbot": "^5.1.27", "js-cookie": "^3.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-redux": "^9.2.0", "react-router": "^7.7.0", "yup": "^1.6.1"}, "devDependencies": {"@react-router/dev": "^7.7.0", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}