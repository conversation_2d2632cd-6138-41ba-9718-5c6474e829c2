# Application Configuration
VITE_APP_NAME="Admin Panel"
VITE_APP_VERSION="1.0.0"
VITE_API_BASE_URL="http://localhost:3001/api"

# Security Configuration
VITE_ENCRYPTION_KEY="your-32-character-encryption-key-here"
VITE_JWT_SECRET="your-jwt-secret-key-here"
VITE_REFRESH_TOKEN_SECRET="your-refresh-token-secret-here"
VITE_CSRF_SECRET="your-csrf-secret-here"

# Session & Authentication
VITE_SESSION_TIMEOUT="3600000"
VITE_MAX_LOGIN_ATTEMPTS="5"
VITE_LOCKOUT_DURATION="900000"

# Rate Limiting
VITE_RATE_LIMIT_WINDOW="900000"
VITE_RATE_LIMIT_MAX="100"

# Database
VITE_MONGODB_URI="mongodb://localhost:27017/admin_panel"

# Features
VITE_ENABLE_AUDIT_LOGS="true"
VITE_ENABLE_TWO_FACTOR="true"
VITE_ENABLE_EMAIL_VERIFICATION="true"

# File Upload
VITE_MAX_FILE_SIZE="10485760"
VITE_ALLOWED_FILE_TYPES="image/*,application/pdf,.doc,.docx,.xls,.xlsx"

# Email Configuration (for production)
VITE_SMTP_HOST="smtp.gmail.com"
VITE_SMTP_PORT="587"
VITE_SMTP_USER="<EMAIL>"
VITE_SMTP_PASS="your-app-password"

# Development
NODE_ENV="development"
VITE_DEBUG="true"
